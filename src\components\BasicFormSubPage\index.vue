<template>
  <div class="basic-form-sub-page">
    <div v-loading="loading" class="content">
      <BasicFormSubmit
        ref="formRef"
        :obj-list="innerOptions.formConfigList"
        :data="innerOptions.formData"
        label-position="top"
        label-width="100px"
        :is-view="isView"
      >
        <template #sub-slot="{ propName }">
          <slot :name="propName"></slot>
        </template>
      </BasicFormSubmit>
      <div v-if="!isView" class="footer">
        <el-button type="primary" :loading="btnLoading" @click="handleConfirm(false)">
          确定
        </el-button>
        <el-button @click="handleClose">取消</el-button>
        <slot name="btns"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isObject } from '@/utils/is'
import { throttle } from 'lodash-es'

defineOptions({ name: 'BasicFormSubPage' })

const message = useMessage()

interface OptionsType {
  title: string // 标题
  type: string // 表单类型 add新增 edit编辑 detail详情
  formConfigList: any // 表单配置
  formData: any // 表单数据
  queryApi: Function // 查询接口
  queryParams?: Object // 查询参数
  submitApi: Function // 提交接口
  afterFetch?: Function // 查询成功后回调
  beforeSubmit?: Function // 提交前回调
  afterSubmit?: Function // 提交成功后回调
  onClose?: Function // 关闭回调
  change?: Function // 表单数据变化回调
}

const loading = ref(false)
const btnLoading = ref(false)
const formRef = ref()
const isView = ref(false)
const innerOptions = ref<OptionsType>({
  title: '', // 标题
  type: '', // 表单类型 add新增 edit编辑 detail详情
  formConfigList: [], // 表单配置
  formData: {}, // 表单数据
  queryApi: () => {}, // 查询接口
  queryParams: {}, // 查询参数
  submitApi: () => {}, // 提交接口
  afterFetch: () => {}, // 查询成功后回调
  beforeSubmit: () => {}, // 提交前回调
  afterSubmit: () => {}, // 提交成功后回调
  onClose: () => {}, // 关闭回调
  change: () => {} // 表单数据变化回调
})

/**
 *
 * @param id 表单id
 * @param type 表单类型 add新增 edit编辑 detail详情
 * @param options 表单配置参数
 */
const init = async (id: number | null, options: OptionsType) => {
  formRef.value?.resetForm()
  innerOptions.value = options
  isView.value = options.type === 'view'

  if (!innerOptions.value.queryApi) {
    if (options.afterFetch) {
      options.afterFetch(innerOptions.value.formData)
    }
    return
  }

  // (id || options.queryParams) && options.type !== 'add'
  if (id || options.queryParams) {
    loading.value = true
    const params: any = { id, ...options.queryParams }
    if (!id) {
      delete params.id
    }
    try {
      const res = await innerOptions.value.queryApi(params)
      innerOptions.value.formData = res || {}
      // 回调查询成功后
      if (options.afterFetch) {
        options.afterFetch(innerOptions.value.formData)
      }
    } finally {
      loading.value = false
    }
  }
}

const handleConfirm = throttle(async (step = false, callback: any = null) => {
  const valid = await formRef.value?.submitForm()
  if (!valid) return false

  const refList = formRef.value.uploadFileFinishList()
  const unfinish = refList?.filter((i: any) => !i.uploadFinish) || []
  if (unfinish.length > 0) {
    message.warning('请等待文件上传完成')
    return
  }

  try {
    btnLoading.value = true
    const { formData, type, beforeSubmit, submitApi, afterSubmit } = innerOptions.value
    // 提交表单数据
    // 回调提交前
    let pass = true
    if (beforeSubmit) {
      pass = await beforeSubmit(formData)
    }

    // 提交前外部如果返回false，则不提交
    if (pass === false) return

    let res: any = {}
    if (isObject(pass)) {
      res = await submitApi(pass)
    } else {
      res = await submitApi(formData)
      if (type === 'edit' && formData.id) {
        res = formData.id
      }
    }

    if (callback) {
      callback(res)
    }

    if (!step) {
      message.success('操作成功')
      setTimeout(() => {
        if (afterSubmit) {
          afterSubmit()
        }
      }, 400)
    }
  } finally {
    btnLoading.value = false
  }
}, 2000)

const handleClose = () => {
  formRef.value?.resetForm()
  if (innerOptions.value.onClose) {
    innerOptions.value.onClose()
  }
}

const setFormConfig = (formConfig: any) => {
  innerOptions.value.formConfigList = formConfig
}

const close = () => {
  handleClose()
}

const submitAudit = throttle(async () => {
  let response: any // 可能是业务数据，也可能是false
  const valid = await handleConfirm(true, (data: any) => {
    response = data
  })

  if (valid === false) {
    response = false
  }
  return response
}, 2000)

watch(
  () => innerOptions.value.formData,
  () => {
    if (innerOptions.value.change) {
      innerOptions.value.change(innerOptions.value.formData)
    }
  },
  { deep: true }
)

defineExpose({ init, setFormConfig, close, submitAudit })
</script>

<style lang="scss" scoped>
.footer {
  padding: 10px 0;
  :deep(.el-button) {
    width: 140px;
  }
}
</style>
