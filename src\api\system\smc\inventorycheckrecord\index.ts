import request from '@/config/axios'

// 盘点记录 VO
export interface InventoryCheckRecordVO {
  id: number // 主键id
  checkNumber: string // 盘点单号
  cabinetId: number // 药柜ID
  checkType: string // 盘点方式:auto/manual
  checkDate: Date // 盘点日期
  totalMedicineCount: number // 药品总数
  differenceCount: number // 差异数量
  checkStatus: string // 盘点状态:normal/abnormal/failed
  checkStartTime: Date // 盘点开始时间
  checkEndTime: Date // 盘点结束时间
  correctionTime: Date // 修正时间
  correctionNote: string // 修正说明
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
  checkOperatorId: number // 盘点操作人
  correctionOperatorId: number // 修正人员
}

// 盘点记录 API
export const InventoryCheckRecordApi = {
  // 查询盘点记录分页
  getInventoryCheckRecordPage: async (params: any) => {
    return await request.get({ url: `/system/inventory-check-record/page`, params })
  },
  // 查询盘点记录列表
  getInventoryCheckRecordList: async (params) => {
    return await request.get({ url: `/system/inventory-check-record/list`, params })
  },

  // 查询盘点记录详情
  getInventoryCheckRecord: async (params: any) => {
    return await request.get({ url: `/system/inventory-check-record/get`, params })
  },

  // 新增盘点记录
  createInventoryCheckRecord: async (data: InventoryCheckRecordVO) => {
    return await request.post({ url: `/system/inventory-check-record/create`, data })
  },

  // 修改盘点记录
  updateInventoryCheckRecord: async (data: InventoryCheckRecordVO) => {
    return await request.put({ url: `/system/inventory-check-record/update`, data })
  },

  // 删除盘点记录
  deleteInventoryCheckRecord: async (id: number) => {
    return await request.delete({ url: `/system/inventory-check-record/delete?id=` + id })
  },

  // 导出盘点记录 Excel
  exportInventoryCheckRecord: async (params) => {
    return await request.download({ url: `/system/inventory-check-record/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/inventory-check-record/get-import-template` })
  },

  // 导入盘点记录 Excel
  importInventoryCheckRecord: async (formData) => {
    return await request.upload({ url: `/system/inventory-check-record/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/inventory-check-record/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}