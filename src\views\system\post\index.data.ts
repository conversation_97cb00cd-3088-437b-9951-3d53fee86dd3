import { DICT_TYPE, getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '岗位名称',
      prop: 'name'
    },
    {
      component: 'input',
      label: '岗位编码',
      prop: 'code'
    },
    {
      component: 'select',
      label: '状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    }
  ]
}

export const tableData: any = ref({
  columns: [
    // { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    { label: '岗位编号', prop: 'id', minWidth: 60 },
    { label: '岗位名称', prop: 'name', minWidth: 160, tooltip: true },
    { label: '岗位编码', prop: 'code', minWidth: 140, tooltip: true },
    { label: '角色标识', prop: 'code', minWidth: 100, tooltip: true },
    { label: '岗位顺序', prop: 'sort', minWidth: 60 },
    { label: '岗位备注', prop: 'remark', minWidth: 140, tooltip: true },
    {
      label: '状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '岗位标题',
        prop: 'name',
        rules: [{ required: true, message: '岗位标题不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '岗位编码',
        prop: 'code',
        rules: [{ required: true, message: '岗位编码不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '岗位顺序',
        prop: 'sort',
        rules: [{ required: true, message: '岗位顺序不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      }
    ]
  }
])
