<template>
  <BasicPage :tabs="['角色管理']">
    <template #action>
      <BasicButtonExport perm-code="system:role:export" file-name="角色管理" :params="{ ...searchForm }" :export-api="RoleApi.exportRole" />
      <el-button v-hasPermi="['system:role:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
  </BasicPage>
  <!-- 表单弹窗：菜单权限 -->
  <RoleAssignMenuForm ref="assignMenuFormRef" @success="reload" />
  <!-- 表单弹窗：数据权限 -->
  <RoleDataPermissionForm ref="dataPermissionFormRef" @success="reload" />
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as RoleApi from '@/api/system/role'
import RoleAssignMenuForm from './RoleAssignMenuForm.vue'
import RoleDataPermissionForm from './RoleDataPermissionForm.vue'

defineOptions({ name: 'SystemRole' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '编辑', permCode: 'system:role:update', callback: handleEdit },
    { label: '菜单权限', permCode: 'system:permission:assign-role-menu', callback: openAssignMenuForm },
    { label: '数据权限', permCode: 'system:permission:assign-role-data-scope', callback: openDataPermissionForm },
    {
      label: '删除',
      permCode: 'system:role:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: RoleApi.getRolePage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

/** 数据权限操作 */
const dataPermissionFormRef = ref()
async function openDataPermissionForm(row: RoleApi.RoleVO) {
  dataPermissionFormRef.value.open(row)
}

/** 菜单权限操作 */
const assignMenuFormRef = ref()
async function openAssignMenuForm(row: RoleApi.RoleVO) {
  assignMenuFormRef.value.open(row)
}

const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: RoleApi.getRole,
    submitApi: id ? RoleApi.updateRole : RoleApi.createRole,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

// function handleDetail({ id }) {
//   openFormDialog(id, 'detail')
// }
async function handleDelete({ id }) {
  await message.delConfirm()
  await RoleApi.deleteRole(id)
  message.success(t('common.delSuccess'))
  reload()
}
</script>

<style></style>
