import request from '@/config/axios'

// 开关锁记录 VO
export interface LockOperationRecordVO {
  id: number // 主键id
  cabinetId: number // 药柜ID
  drawerId: number // 抽屉ID
  slotId: number // 格口ID
  drawerName: string // 抽屉号
  slotName: string // 格口号
  operationType: string // 操作类型:inbound/outbound
  businessType: string // 业务类型:补药入柜/取药出柜/分药出柜等
  lockAction: string // 锁操作:open/close
  operateTime: Date // 锁操作时间
  operationDuration: number // 操作时长(秒)
  operatorName: string // 操作人员
  relatedRecordId: number // 关联业务记录ID
  videoUrl: string // 操作视频链接
  lockStatus: string // 锁状态:opened/closed/abnormal
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 开关锁记录 API
export const LockOperationRecordApi = {
  // 查询开关锁记录分页
  getLockOperationRecordPage: async (params: any) => {
    return await request.get({ url: `/system/lock-operation-record/page`, params })
  },
  // 查询开关锁记录列表
  getLockOperationRecordList: async (params) => {
    return await request.get({ url: `/system/lock-operation-record/list`, params })
  },

  // 查询开关锁记录详情
  getLockOperationRecord: async (params: any) => {
    return await request.get({ url: `/system/lock-operation-record/get`, params })
  },

  // 新增开关锁记录
  createLockOperationRecord: async (data: LockOperationRecordVO) => {
    return await request.post({ url: `/system/lock-operation-record/create`, data })
  },

  // 修改开关锁记录
  updateLockOperationRecord: async (data: LockOperationRecordVO) => {
    return await request.put({ url: `/system/lock-operation-record/update`, data })
  },

  // 删除开关锁记录
  deleteLockOperationRecord: async (id: number) => {
    return await request.delete({ url: `/system/lock-operation-record/delete?id=` + id })
  },

  // 导出开关锁记录 Excel
  exportLockOperationRecord: async (params) => {
    return await request.download({ url: `/system/lock-operation-record/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/lock-operation-record/get-import-template` })
  },

  // 导入开关锁记录 Excel
  importLockOperationRecord: async (formData) => {
    return await request.upload({ url: `/system/lock-operation-record/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/lock-operation-record/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}