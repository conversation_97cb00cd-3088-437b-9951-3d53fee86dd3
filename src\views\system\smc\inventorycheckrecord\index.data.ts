import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
  import {CabinetApi} from "@/api/system/smc/cabinet";
  import {UsersApi} from "@/api/system/user/userOption";

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '盘点单号',
      prop: 'checkNumber',
      placeholder: '请输入盘点单号'
    },
    {
      component: 'select',
      label: '药柜ID',
      prop: 'cabinetId',
      placeholder: '请选择药柜ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: CabinetApi.getCabinetList,
    },
    {
      component: 'select',
      label: '盘点方式:auto/manual',
      prop: 'checkType',
      placeholder: '请选择盘点方式:auto/manual',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)
    },
    {
      component: 'datePickerRange',
      label: '盘点日期',
      prop: 'checkDate',
      startPlaceholder: '盘点日期开始日期',
      endPlaceholder: '盘点日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'number',
      label: '药品总数',
      prop: 'totalMedicineCount',
      placeholder: '请输入药品总数'
    },
    {
      component: 'number',
      label: '差异数量',
      prop: 'differenceCount',
      placeholder: '请输入差异数量'
    },
    {
      component: 'select',
      label: '盘点状态:normal/abnormal/failed',
      prop: 'checkStatus',
      placeholder: '请选择盘点状态:normal/abnormal/failed',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_CHECK_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '盘点开始时间',
      prop: 'checkStartTime',
      startPlaceholder: '盘点开始时间开始日期',
      endPlaceholder: '盘点开始时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'datePickerRange',
      label: '盘点结束时间',
      prop: 'checkEndTime',
      startPlaceholder: '盘点结束时间开始日期',
      endPlaceholder: '盘点结束时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'datePickerRange',
      label: '修正时间',
      prop: 'correctionTime',
      startPlaceholder: '修正时间开始日期',
      endPlaceholder: '修正时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'input',
      label: '修正说明',
      prop: 'correctionNote',
      placeholder: '请输入修正说明'
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'select',
      label: '盘点操作人',
      prop: 'checkOperatorId',
      placeholder: '请选择盘点操作人',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: UsersApi.getUsersList,
    },
    {
      component: 'select',
      label: '修正人员',
      prop: 'correctionOperatorId',
      placeholder: '请选择修正人员',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: UsersApi.getUsersList,
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '盘点单号',
      prop: 'checkNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药柜ID',
      prop: 'cabinetIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '盘点方式:auto/manual',
      prop: 'checkType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.checkType)
    },
    {
      label: '盘点日期',
      prop: 'checkDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品总数',
      prop: 'totalMedicineCount',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '差异数量',
      prop: 'differenceCount',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '盘点状态:normal/abnormal/failed',
      prop: 'checkStatus',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_CHECK_STATUS, row.checkStatus)
    },
    {
      label: '盘点开始时间',
      prop: 'checkStartTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '盘点结束时间',
      prop: 'checkEndTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '修正时间',
      prop: 'correctionTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '修正说明',
      prop: 'correctionNote',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '盘点操作人',
      prop: 'checkOperatorIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '修正人员',
      prop: 'correctionOperatorIdName',
      tooltip: true,
      minWidth: 100
    },
  ]
})

export const formConfig = ref([
  {
    title: '盘点记录',
    items: [
      {
        component: 'input',
        label: '盘点单号',
        prop: 'checkNumber',
        placeholder: '请输入盘点单号',
        rules: [
              { required: true, message: '盘点单号不能为空', trigger: 'blur' },
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '药柜ID',
        prop: 'cabinetId',
        placeholder: '请选择药柜ID',
        params: {},
        filterable: true,
        propName: 'cabinetIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: CabinetApi.getCabinetList,
        rules: [{ required: true, message: '药柜ID不能为空', trigger: 'change' }]
      },
      {
        component: 'select',
        label: '盘点方式:auto/manual',
        prop: 'checkType',
        placeholder: '请选择盘点方式:auto/manual',
        params: {},
        filterable: true,
        options: getStrDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING),
        rules: [{ required: true, message: '盘点方式:auto/manual不能为空', trigger: 'change' }]
      },
      {
        component: 'datePicker',
        label: '盘点日期',
        prop: 'checkDate',
        placeholder: '选择盘点日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
          rules: [{ required: true, message: '盘点日期不能为空', trigger: 'blur' }]
      },
      {
        component: 'number',
        label: '药品总数',
        prop: 'totalMedicineCount',
        max: 9999999999,
        placeholder: '请输入药品总数',
      },
      {
        component: 'number',
        label: '差异数量',
        prop: 'differenceCount',
        max: 9999999999,
        placeholder: '请输入差异数量',
      },
      //todo  单选框 radio     label: '盘点状态:normal/abnormal/failed',      prop: 'checkStatus',
      {
        component: 'select',
        label: '盘点状态:normal/abnormal/failed',
        prop: 'checkStatus',
        placeholder: '请选择盘点状态:normal/abnormal/failed',
          options: getStrDictOptions(DICT_TYPE.SMC_CHECK_STATUS),
      },
      {
        component: 'datePicker',
        label: '盘点开始时间',
        prop: 'checkStartTime',
        placeholder: '选择盘点开始时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'datePicker',
        label: '盘点结束时间',
        prop: 'checkEndTime',
        placeholder: '选择盘点结束时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'datePicker',
        label: '修正时间',
        prop: 'correctionTime',
        placeholder: '选择修正时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'input',
        label: '修正说明',
        prop: 'correctionNote',
        placeholder: '请输入修正说明',
        rules: [
              { max: 65535, message: '长度不能超过65535个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        max: 999,
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '盘点操作人',
        prop: 'checkOperatorId',
        placeholder: '请选择盘点操作人',
        params: {},
        filterable: true,
        propName: 'checkOperatorIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: UsersApi.getUsersList,
      },
      {
        component: 'select',
        label: '修正人员',
        prop: 'correctionOperatorId',
        placeholder: '请选择修正人员',
        params: {},
        filterable: true,
        propName: 'correctionOperatorIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: UsersApi.getUsersList,
      },
    ]
  }
])
