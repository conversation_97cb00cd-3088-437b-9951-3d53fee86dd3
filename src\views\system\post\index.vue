<template>
  <BasicPage :tabs="['岗位管理']">
    <template #action>
      <BasicButtonExport perm-code="system:post:export" file-name="岗位管理" :params="{ ...searchForm }" :export-api="PostApi.exportPost" />
      <el-button v-hasPermi="['system:post:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as PostA<PERSON> from '@/api/system/post'


defineOptions({ name: 'SystemPost' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '编辑', permCode: 'system:post:update', callback: handleEdit },
    {
      label: '删除',
      permCode: 'system:post:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: PostApi.getPostPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: PostApi.getPost,
    submitApi: id ? PostApi.updatePost :PostApi.createPost,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await PostApi.deletePost(id)
  message.success(t('common.delSuccess'))
  reload()
}
</script>

<style></style>
