<template>
  <BasicPage :tabs="['流程表单']">
    <template #action>
      <el-button v-hasPermi="['bpm:form:create']" @click="openForm">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
  </BasicPage>
  <!-- 表单详情的弹窗 -->
  <Dialog v-model="detailVisible" title="表单详情" width="800">
    <form-create :option="detailData.option" :rule="detailData.rule" />
  </Dialog>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as FormApi from '@/api/bpm/form'
import { setConfAndFields2 } from '@/utils/formCreate'

defineOptions({ name: 'BpmForm' })

const message = useMessage()
const { t } = useI18n()
const { currentRoute, push } = useRouter() // 路由
const detailVisible = ref(false)
const detailData = ref({
  rule: [],
  option: {}
})
const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '编辑', permCode: 'bpm:form:update', callback: openForm },
    { label: '详情', permCode: 'bpm:form:query', callback: openDetail },
    {
      label: '删除',
      permCode: 'bpm:form:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: FormApi.getFormPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

/** 添加/修改操作 */
function openForm({ id }) {
  const toRouter: { name: string; query?: { id: number } } = {
    name: 'BpmFormEditor'
  }
  // 表单新建的时候id传的是event需要排除
  if (typeof id === 'number') {
    toRouter.query = {
      id
    }
  }
  push(toRouter)
}

async function openDetail({ id }) {
  // 设置表单
  const data = await FormApi.getForm(id)
  setConfAndFields2(detailData, data.conf, data.fields)
  // 弹窗打开
  detailVisible.value = true
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await FormApi.deleteForm(id)
  message.success(t('common.delSuccess'))
  reload()
}

/**表单保存返回后重新加载列表 */
watch(
  () => currentRoute.value,
  () => {
    reload()
  },
  {
    immediate: true
  }
)
</script>

<style></style>
