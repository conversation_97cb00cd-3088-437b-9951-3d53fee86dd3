<template>
  <div class="components-info-container">
    <div class="info-header">
      <h3 class="section-title">其他部件</h3>
    </div>

    <div class="components-content">
      <!-- 硬件状态 -->
      <div class="component-section">
        <h4 class="subsection-title">硬件状态</h4>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="status-card">
              <div class="status-icon">
                <Icon icon="ep:cpu" size="24" />
              </div>
              <div class="status-info">
                <div class="status-title">CPU状态</div>
                <div class="status-value">正常</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-card">
              <div class="status-icon">
                <Icon icon="ep:monitor" size="24" />
              </div>
              <div class="status-info">
                <div class="status-title">显示屏</div>
                <div class="status-value">正常</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-card">
              <div class="status-icon">
                <Icon icon="ep:camera" size="24" />
              </div>
              <div class="status-info">
                <div class="status-title">摄像头</div>
                <div class="status-value">正常</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24" class="mt-4">
          <el-col :span="8">
            <div class="status-card">
              <div class="status-icon">
                <Icon icon="ep:fingerprint" size="24" />
              </div>
              <div class="status-info">
                <div class="status-title">指纹识别</div>
                <div class="status-value">正常</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-card">
              <div class="status-icon">
                <Icon icon="ep:key" size="24" />
              </div>
              <div class="status-info">
                <div class="status-title">电子锁</div>
                <div class="status-value">正常</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="status-card">
              <div class="status-icon">
                <Icon icon="ep:connection" size="24" />
              </div>
              <div class="status-info">
                <div class="status-title">网络连接</div>
                <div class="status-value">正常</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 传感器状态 -->
      <div class="component-section">
        <h4 class="subsection-title">传感器状态</h4>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="sensor-card">
              <div class="sensor-name">温度传感器</div>
              <div class="sensor-value">23.5°C</div>
              <div class="sensor-status normal">正常</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="sensor-card">
              <div class="sensor-name">湿度传感器</div>
              <div class="sensor-value">45%</div>
              <div class="sensor-status normal">正常</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="sensor-card">
              <div class="sensor-name">门磁传感器</div>
              <div class="sensor-value">关闭</div>
              <div class="sensor-status normal">正常</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 系统信息 -->
      <div class="component-section">
        <h4 class="subsection-title">系统信息</h4>
        <div class="system-info">
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">操作系统：</label>
                <span class="info-value">Linux Ubuntu 20.04</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">软件版本：</label>
                <span class="info-value">V1.0.0</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">内存使用：</label>
                <span class="info-value">2.1GB / 4GB</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">存储空间：</label>
                <span class="info-value">45GB / 128GB</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">运行时间：</label>
                <span class="info-value">15天 8小时 32分钟</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label class="info-label">最后重启：</label>
                <span class="info-value">2025-04-15 09:30:00</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  cabinetId: {
    type: String,
    required: true
  }
})
</script>

<style scoped lang="scss">
.components-info-container {
  .info-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .components-content {
    .component-section {
      margin-bottom: 32px;

      .subsection-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }

      .status-card {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .status-icon {
          margin-right: 12px;
          color: #52c41a;
        }

        .status-info {
          .status-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          }

          .status-value {
            font-size: 16px;
            font-weight: 500;
            color: #262626;
          }
        }
      }

      .sensor-card {
        padding: 16px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        text-align: center;

        .sensor-name {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .sensor-value {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 8px;
        }

        .sensor-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;

          &.normal {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }

          &.warning {
            background: #fffbe6;
            color: #faad14;
            border: 1px solid #ffe58f;
          }

          &.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
          }
        }
      }

      .system-info {
        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          min-height: 32px;

          .info-label {
            width: 120px;
            color: #666;
            font-weight: 500;
            flex-shrink: 0;
          }

          .info-value {
            color: #262626;
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
