import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
  import {InventoryCheckRecordApi} from "@/api/system/smc/inventorycheckrecord";
  import {CabinetApi} from "@/api/system/smc/cabinet";
  import {MedicineApi} from "@/api/system/smc/medicine";

export const formSearchConfig = {
  itemList: [
    {
      component: 'select',
      label: '盘点记录ID',
      prop: 'checkRecordId',
      placeholder: '请选择盘点记录ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: InventoryCheckRecordApi.getInventoryCheckRecordList,
    },
    {
      component: 'select',
      label: '药柜ID',
      prop: 'cabinetId',
      placeholder: '请选择药柜ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: CabinetApi.getCabinetList,
    },
    {
      component: 'input',
      label: '抽屉ID',
      prop: 'drawerId',
      placeholder: '请输入抽屉ID'
    },
    {
      component: 'input',
      label: '格口ID',
      prop: 'slotId',
      placeholder: '请输入格口ID'
    },
    {
      component: 'select',
      label: '药品ID',
      prop: 'medicineId',
      placeholder: '请选择药品ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: MedicineApi.getMedicineList,
    },
    {
      component: 'input',
      label: '单据药品批次号',
      prop: 'batchNumber',
      placeholder: '请输入单据药品批次号'
    },
    {
      component: 'input',
      label: '药品追溯码',
      prop: 'traceCode',
      placeholder: '请输入药品追溯码'
    },
    {
      component: 'datePickerRange',
      label: '生产日期',
      prop: 'productionDate',
      startPlaceholder: '生产日期开始日期',
      endPlaceholder: '生产日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'number',
      label: '药品有效期(月)',
      prop: 'shelfLife',
      placeholder: '请输入药品有效期(月)'
    },
    {
      component: 'datePickerRange',
      label: '有效期截止日期',
      prop: 'expiryDate',
      startPlaceholder: '有效期截止日期开始日期',
      endPlaceholder: '有效期截止日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'number',
      label: '系统库存数量',
      prop: 'systemQuantity',
      placeholder: '请输入系统库存数量'
    },
    {
      component: 'number',
      label: '实际盘点数量',
      prop: 'actualQuantity',
      placeholder: '请输入实际盘点数量'
    },
    {
      component: 'number',
      label: '差异数量',
      prop: 'differenceQuantity',
      placeholder: '请输入差异数量'
    },
    {
      component: 'number',
      label: '修正后数量',
      prop: 'correctedQuantity',
      placeholder: '请输入修正后数量'
    },
    {
      component: 'select',
      label: '盘点结果:normal/difference/corrected',
      prop: 'correctionStatus',
      placeholder: '请选择盘点结果:normal/difference/corrected',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_CHECK_STATUS)
    },
    {
      component: 'input',
      label: '存放位置',
      prop: 'slotPosition',
      placeholder: '请输入存放位置'
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '盘点记录ID',
      prop: 'checkRecordIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药柜ID',
      prop: 'cabinetIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '抽屉ID',
      prop: 'drawerIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '格口ID',
      prop: 'slotIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品ID',
      prop: 'medicineIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单据药品批次号',
      prop: 'batchNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品追溯码',
      prop: 'traceCode',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '生产日期',
      prop: 'productionDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品有效期(月)',
      prop: 'shelfLife',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '有效期截止日期',
      prop: 'expiryDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '系统库存数量',
      prop: 'systemQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '实际盘点数量',
      prop: 'actualQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '差异数量',
      prop: 'differenceQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '修正后数量',
      prop: 'correctedQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '盘点结果:normal/difference/corrected',
      prop: 'correctionStatus',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_CHECK_STATUS, row.correctionStatus)
    },
    {
      label: '存放位置',
      prop: 'slotPosition',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '盘点明细',
    items: [
    ]
  }
])
