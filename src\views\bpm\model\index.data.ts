import { DICT_TYPE, getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '流程标识',
      prop: 'key'
    },
    {
      component: 'input',
      label: '流程名称',
      prop: 'name'
    },
    {
      label: '流程分类',
      slot: 'category'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '流程标识', prop: 'key', minWidth: 200 },
    { label: '流程名称', slot: 'name', minWidth: 200, tooltip: true },
    { label: '流程图标', slot: 'icon', minWidth: 100, tooltip: true },
    { label: '流程分类', slot: 'categoryName', minWidth: 100, tooltip: true },
    { label: '表单信息', slot: 'formType', minWidth: 180, tooltip: true },
    { label: '创建时间', minWidth: 180, prop: 'createTime' },
    { label: '流程版本', minWidth: 100, slot: 'version' },
    { label: '激活状态', minWidth: 85, slot: 'suspensionState' },
    { label: '部署时间', minWidth: 180, prop: 'deploymentTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '流程标识',
        prop: 'key',
        rules: [{ required: true, message: '流程标识不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '流程名称',
        prop: 'name',
        rules: [{ required: true, message: '流程名称不能为空', trigger: 'blur' }]
      },
      {
        label: '流程分类',
        slot: 'category',
        rules: [{ required: true, message: '流程分类不能为空', trigger: 'blur' }]
      },
      {
        label: '流程图标',
        slot: 'icon',
        rules: [{ required: true, message: '流程图标不能为空', trigger: 'blur' }]
      },
      {
        component: 'textarea',
        label: '流程描述',
        prop: 'description'
      },
      {
        component: 'select',
        label: '表单类型',
        prop: 'formType',
        options: getIntDictOptions(DICT_TYPE.BPM_MODEL_FORM_TYPE)
      },
      {
        label: '流程表单',
        slot: 'formId'
      },
      {
        component: 'input',
        label: '表单提交路由',
        prop: 'formCustomCreatePath'
      },
      {
        component: 'input',
        label: '表单查看地址',
        prop: 'formCustomViewPath'
      }
    ]
  }
])

export const filterFormConfig = (type: any, choose?: any) => {
  let config: any = []
  if (type == 'add') {
    config = formConfig.value[0].items.filter((val: any) => ['key', 'name', 'description'].includes(val.prop))
    formConfig.value[0].items.forEach((item: any) => {
      if (item.prop === 'key' || item.prop === 'name') {
        delete item.readonly
      }
    })
  } else if (type == 'edit') {
    formConfig.value[0].items.forEach((item: any) => {
      if (item.prop === 'key' || item.prop === 'name') {
        item.readonly = true
      }
    })

    if (choose == 'formId') {
      config = formConfig.value[0].items.filter((val: any) => !['formCustomCreatePath', 'formCustomViewPath'].includes(val.prop))
    } else if (choose == 'formCustom') {
      config = formConfig.value[0].items.filter((val: any) => val.slot !== 'formId')
    } else {
      config = formConfig.value[0].items.filter(
        (val: any) => !['formCustomCreatePath', 'formCustomViewPath'].includes(val.prop) && val.slot !== 'formId'
      )
    }
  }
  return [{ items: config }]
}
