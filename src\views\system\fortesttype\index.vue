<template>
  <BasicPage :tabs="['测试而已的类型管理']">
    <template #action>
      <BasicButtonImport
              perm-code="system:for-test-type:import"
              file-name="测试而已的类型管理"
              :template-api="ForTestTypeApi.importTemplate"
              :import-api="ForTestTypeApi.importForTestType"
              :exportError-file-api="ForTestTypeApi.exportErrorFile"
              @success="handlerImportSuccess"
      />
      <BasicButtonExport
              perm-code="system:for-test-type:export"
              file-name="测试而已的类型管理"
              :export-api="ForTestTypeApi.exportForTestType"
      />
      <el-button v-hasPermi="['system:for-test-type:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register"/>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { ForTestTypeApi} from '@/api/system/fortesttype'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:for-test-type:update', callback: handleEdit },
    { label: '查看', permCode: 'system:for-test-type:query', callback: handleDetail },
    { label: '删除', permCode: 'system:for-test-type:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: ForTestTypeApi.getForTestTypePage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: ForTestTypeApi.getForTestType,
    submitApi: id ? ForTestTypeApi.updateForTestType : ForTestTypeApi.createForTestType
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await ForTestTypeApi.deleteForTestType(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>