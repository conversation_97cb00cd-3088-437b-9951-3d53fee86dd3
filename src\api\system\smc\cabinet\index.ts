import request from '@/config/axios'

// 药柜设备信息 VO
export interface CabinetVO {
  cabinetCode: string // 药柜编号
  location: string // 存放位置
  ip: string // IP地址
  hardwareVersion: string // 硬件版本号
  drugSlotCount: number // 药格数
  recycleSlotCount: number // 回收格数
  currentMedicineCount: number // 当前存放药品数
  deptIds: string // 可使用部门ID列表(JSON数组)
  remark: string // 备注
}

// 药柜设备信息 API
export const CabinetApi = {
  // 查询药柜设备信息分页
  getCabinetPage: async (params: any) => {
    return await request.get({ url: `/system/cabinet/page`, params })
  },
  // 查询药柜设备信息列表
  getCabinetList: async (params) => {
    return await request.get({ url: `/system/cabinet/list`, params })
  },

  // 查询药柜设备信息详情
  getCabinet: async (params: any) => {
    return await request.get({ url: `/system/cabinet/get`, params })
  },

  // 新增药柜设备信息
  createCabinet: async (data: CabinetVO) => {
    return await request.post({ url: `/system/cabinet/create`, data })
  },

  // 修改药柜设备信息
  updateCabinet: async (data: CabinetVO) => {
    return await request.put({ url: `/system/cabinet/update`, data })
  },

  // 删除药柜设备信息
  deleteCabinet: async (id: number) => {
    return await request.delete({ url: `/system/cabinet/delete?id=` + id })
  },

  // 导出药柜设备信息 Excel
  exportCabinet: async (params) => {
    return await request.download({ url: `/system/cabinet/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/cabinet/get-import-template` })
  },

  // 导入药柜设备信息 Excel
  importCabinet: async (formData) => {
    return await request.upload({ url: `/system/cabinet/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/cabinet/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}