<template>
  <BasicPage :tabs="['单据管理']">
    <template #action>
      <BasicButtonImport
              perm-code="system:documents:import"
              file-name="单据管理"
              :template-api="DocumentsApi.importTemplate"
              :import-api="DocumentsApi.importDocuments"
              :exportError-file-api="DocumentsApi.exportErrorFile"
              @success="handlerImportSuccess"
      />
      <BasicButtonExport
              perm-code="system:documents:export"
              file-name="单据管理"
              :params="{ ...searchForm }"
              :export-api="DocumentsApi.exportDocuments"
      />
      <el-button v-hasPermi="['system:documents:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register"/>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { DocumentsApi} from '@/api/system/smc/documents'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:documents:update', callback: handleEdit },
    { label: '查看', permCode: 'system:documents:query', callback: handleDetail },
    { label: '删除', permCode: 'system:documents:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: DocumentsApi.getDocumentsPage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: DocumentsApi.getDocuments,
    submitApi: id ? DocumentsApi.updateDocuments : DocumentsApi.createDocuments
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await DocumentsApi.deleteDocuments(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>