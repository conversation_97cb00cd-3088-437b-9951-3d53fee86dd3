<template>
  <BasicPage :tabs="['字典数据']">
    <template #action>
      <BasicButtonExport
        perm-code="system:post:export"
        file-name="字典数据"
        :params="{ ...searchForm }"
        :export-api="DictDataApi.exportDictData"
      />
      <el-button v-if="userStore.getIsDeveloper || beSystem==0" v-hasPermi="['system:dict:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn">
      <template #name>
        <BasicSelect
          v-model="searchForm.dictType"
          :options="dictTypeList"
          :field-names="{ label: 'name', value: 'type' }"
          filterable
          placeholder="字典名称"
        />
      </template>
    </BasicFormSearch>
    <BasicTable @register="register" />
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as DictDataApi from '@/api/system/dict/dict.data'
import * as DictTypeApi from '@/api/system/dict/dict.type'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'SystemDictData' })

const message = useMessage()
const { t } = useI18n()
const route = useRoute() // 路由

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const userStore = useUserStore() // 用户信息
const beSystem = ref(route.params.beSystem)
const dictTypeList = ref([]) // 字典类型的列表

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    {
      label: '编辑',
      hidden: () => {
        return userStore.getIsDeveloper || !beSystem.value == 0
      },
      permCode: 'system:dict:update',
      callback: handleEdit
    },
    {
      label: '删除',
      permCode: 'system:dict:delete',
      hidden: () => {
        return userStore.getIsDeveloper || beSystem.value == 0
      },
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: DictDataApi.getDictDataPage,
  columns: (userStore.getIsDeveloper || beSystem.value == 0) ? totalColumns : tableData.value.columns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    searchForm.value.dictType = route.params.dictType
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = (id: number | null, type: string) => {
  if (type == 'add') {
    formData.value.status = 0
  }
  formData.value.dictType = route.params.dictType
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: DictDataApi.getDictData,
    submitApi: id ? DictDataApi.updateDictData : DictDataApi.createDictData,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await DictDataApi.deleteDictData(id)
  message.success(t('common.delSuccess'))
  reload()
}
onMounted(async () => {
  // 查询字典（精简)列表
  dictTypeList.value = await DictTypeApi.getSimpleDictTypeList({ beSystem: userStore.getIsDeveloper ? '' : 0 })
})
watch(
  () => searchForm.value.dictType,
  (val: any) => {
    beSystem.value = dictTypeList.value.find((e) => e.type == val)?.beSystem || route.params.beSystem
  },
  { deep: true }
)
</script>

<style></style>
