export const tableProps = {
  api: {
    type: [Promise, Function]
  },
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => []
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  tooltip: {
    type: Boolean,
    default: true
  },
  immediate: {
    type: Boolean,
    default: true
  },
  pagination: {
    type: [Object, Boolean],
    default: true
  },
  highlightCurrentRow: {
    type: Boolean,
    default: false
  },
  columnsSetting: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: false
  },
  showSummary: {
    type: Boolean,
    default: false
  },
  border: {
    type: Boolean,
    default: true
  }
};
