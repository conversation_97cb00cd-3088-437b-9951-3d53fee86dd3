<script lang="ts" setup>
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

// eslint-disable-next-line vue/no-reserved-component-names
defineOptions({ name: 'Footer' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('footer')

const appStore = useAppStore()

const title = computed(() => appStore.getTitle)

const year = new Date().getFullYear()

const copyright = computed(() => `Copyright ©${year}-${title.value}`)
</script>

<template>
  <div
    :class="prefixCls"
    class="basic-footer h-[var(--app-footer-height)] bg-[var(--app-content-bg-color)] text-center leading-[var(--app-footer-height)] text-[var(--el-text-color-placeholder)] dark:bg-[var(--el-bg-color)]"
  >
    <span class="text-14px">{{ copyright }}</span>
  </div>
</template>

<style>
.basic-footer {
  position: fixed;
  width: -webkit-fill-available;
  bottom: 0;
  z-index: 88;
}
</style>
