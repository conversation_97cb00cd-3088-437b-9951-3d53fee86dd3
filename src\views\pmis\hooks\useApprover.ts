import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as Definition<PERSON>pi from '@/api/bpm/definition'

const detailRouterList = [
  {
    key: 'pmis_project_tender_document',
    path: '/pmis/project/biddingInfo/documentDetail'
  },
  {
    key: 'pmis_project_tender_result',
    path: '/pmis/project/biddingInfo/resultDetail'
  },
  {
    key: 'pmis_payment_apply',
    path: '/pmis/project/cost/engineeringPayment/detail'
  },
  {
    key: 'pmis_project_control_price',
    path: '/pmis/project/budgeting/detail'
  },
  {
    key: 'pmis_project_accounts_final',
    path: '/pmis/project/finalAccounts/detail'
  },
  {
    key: 'pmis_project_accounts_settlement',
    path: '/pmis/project/finalAccounts/settlementDetail'
  },
  {
    key: 'pmis_project_contract',
    path: '/pmis/contract/contractInfo/detail'
  },
  {
    key: 'pmis_print_approval',
    path: '/pmis/printapproval/detail'
  }
]

export const useApprover = ({
  processDefineKey = undefined as any, // 流程标识
  initFormRef = undefined as any, // 返回的表单实例(DOM Ref)
  submitApi = undefined as any, // 提交审批接口
  batchSubmitApi = undefined as any, // 批量提交审批接口
  resumbitApi = undefined as any, // 重新提交审批接口
  afterSubmit = undefined as any
}) => {
  const router = useRouter()
  const message = useMessage()
  const { t } = useI18n()

  const formRef = ref()

  const approverButtons = (status: number, permCode: string) => {
    return [
      {
        label: '提交审批',
        permCode: `pmis:${permCode}:approve-submit`,
        eventName: 'approveSubmit',
        show: [0].includes(status)
      },
      {
        label: '详情',
        permCode: `pmis:${permCode}:approve-detail`,
        eventName: 'approveDetail',
        show: [1, 2, 3, 4].includes(status)
      },
      {
        label: '撤回',
        permCode: `pmis:${permCode}:approve-cancel`,
        eventName: 'approveCancel',
        show: [1].includes(status)
      },
      {
        label: '重新提交',
        permCode: `pmis:${permCode}:approve-resubmit`,
        eventName: 'approveResubmit',
        show: [3, 4].includes(status)
      }
    ]
  }

  // 更新流程标识 并 重新获取流程定义中指定审批人
  let innerProcessDefineKey = processDefineKey
  const setProcessDefineKey = (key: string) => {
    innerProcessDefineKey = key
  }

  let innerSubmitApi = submitApi
  let innerBatchSubmitApi = batchSubmitApi
  let innerResumbitApi = resumbitApi
  const setApi = ({ submitApi = undefined as any, batchSubmitApi = undefined as any, resumbitApi = undefined as any }) => {
    innerSubmitApi = submitApi
    innerBatchSubmitApi = batchSubmitApi
    innerResumbitApi = resumbitApi
  }

  const startUserSelectTasks = ref([]) // 发起人需要选择审批人的用户任务列表
  const needSelectTasks = ref(false) // 是否需要选择审批人
  // 获取流程定义中指定审批人
  const handleSelectTasks = async () => {
    const processDefinitionDetail = await DefinitionApi.getProcessDefinition(undefined, innerProcessDefineKey)
    if (!processDefinitionDetail) {
      message.error('OA 请假的流程模型未配置，请检查！')
      return
    }
    startUserSelectTasks.value = processDefinitionDetail.startUserSelectTasks
    // 设置指定审批人
    needSelectTasks.value = startUserSelectTasks.value?.length > 0
  }
  // 审批提交
  const handleSubmit = async (data: any, isBatch = false) => {
    await handleSelectTasks()
    if (needSelectTasks.value) {
      formRef.value.init({
        id: !isBatch ? data.id : undefined,
        ids: isBatch ? data : undefined,
        title: '选择审批人',
        label: '任务',
        successMsg: '提交审批成功',
        isBatch,
        startUserSelectTasks: startUserSelectTasks.value,
        submitApi: innerSubmitApi,
        batchSubmitApi: innerBatchSubmitApi,
        afterSubmit: () => {
          if (afterSubmit) afterSubmit()
        }
      })
      return
    }
    const msg = isBatch ? '批量提交审批' : '提交审批'
    await message.confirm(`确定${msg}吗？`)
    if (isBatch) {
      await innerBatchSubmitApi({ batchIds: data }) // 批量提交审批的data是数组[1, 2, 3]
    } else {
      await innerSubmitApi({ id: data.id }) // 单个提交审批data是当前行对象
    }
    message.success('提交审批成功')
    if (afterSubmit) afterSubmit()
  }

  // 审批撤回
  const handleCancel = async (data: any) => {
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '取消原因不能为空'
    })
    // 发起取消
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(data.processInstanceId, value)
    message.success('取消成功')

    if (afterSubmit) afterSubmit()
  }

  // 审批重新提交
  const handleResubmit = async (data: any) => {
    const detailRouter: any = detailRouterList.find((item) => item.key === innerProcessDefineKey)
    router.push({
      path: detailRouter.path,
      query: { id: data.id, type: 'edit', resubmit: '1' }
    })

    // await message.confirm('确定重新提交吗？')
    // const requestData = { ...data, status: 0 }
    // delete requestData.id
    // await innerResumbitApi(requestData)
    // message.success('重新提交成功')
    // if (afterSubmit) afterSubmit()
  }

  const handleDetail = (data: any) => {
    router.push({
      name: 'BpmProcessInstanceDetail',
      query: {
        id: data.processInstanceId
      }
    })
  }

  const handleButtonEvent = (eventName: string, data: any) => {
    switch (eventName) {
      case 'approveSubmit':
        handleSubmit(data)
        break
      case 'approveSubmitBatch':
        handleSubmit(data, true)
        break
      case 'approveDetail':
        handleDetail(data)
        break
      case 'approveCancel':
        handleCancel(data)
        break
      case 'approveResubmit':
        handleResubmit(data)
        break
    }
  }

  onMounted(async () => {
    if (initFormRef) {
      formRef.value = await initFormRef()
    }
  })

  return {
    approverButtons,
    handleButtonEvent,
    setApi,
    setProcessDefineKey
  }
}
