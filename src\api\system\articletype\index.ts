import request from '@/config/axios'

// 新闻资讯类型 VO
export interface ArticleTypeVO {
  id: number // 主键id
  name: string // 名称
  parentId: number // 父类id
  iconUrl: string // 分类图标
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 新闻资讯类型 API
export const ArticleTypeApi = {
  // 查询新闻资讯类型列表
  getArticleTypeList: async (params) => {
    return await request.get({ url: `/system/article-type/list`, params })
  },

  // 查询新闻资讯类型详情
  getArticleType: async (params: any) => {
    return await request.get({ url: `/system/article-type/get`, params })
  },

  // 新增新闻资讯类型
  createArticleType: async (data: ArticleTypeVO) => {
    return await request.post({ url: `/system/article-type/create`, data })
  },

  // 修改新闻资讯类型
  updateArticleType: async (data: ArticleTypeVO) => {
    return await request.put({ url: `/system/article-type/update`, data })
  },

  // 删除新闻资讯类型
  deleteArticleType: async (id: number) => {
    return await request.delete({ url: `/system/article-type/delete?id=` + id })
  },

  // 导出新闻资讯类型 Excel
  exportArticleType: async (params) => {
    return await request.download({ url: `/system/article-type/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/article-type/get-import-template` })
  },

  // 导入新闻资讯类型 Excel
  importArticleType: async (formData) => {
    return await request.upload({ url: `/system/article-type/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/article-type/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },
}
