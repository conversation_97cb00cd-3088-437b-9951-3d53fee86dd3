import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '版本名称',
      prop: 'name',
      placeholder: '请输入版本名称'
    },
    {
      component: 'input',
      label: '版本号如 v1.0.0',
      prop: 'version',
      placeholder: '请输入版本号如 v1.0.0'
    },
    {
      component: 'input',
      label: '数字版本号如 1001',
      prop: 'versionNum',
      placeholder: '请输入数字版本号如 1001'
    },
    {
      component: 'select',
      label: 'app应用类型 ',
      prop: 'appType',
      placeholder: '请选择app应用类型',
      options: getStrDictOptions(DICT_TYPE.SYSTEM_APP_TYPE)
    },
    {
      component: 'datePickerRange',
      label: '升级更新时间',
      prop: 'upgradeTime',
      startPlaceholder: '升级更新时间开始日期',
      endPlaceholder: '升级更新时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    {index: true, label: '序号', width: 60, filter: (index: number) => index + 1},
    {
      label: '版本名称',
      prop: 'name',
      minWidth: 80
    },
    {
      label: '版本号',
      prop: 'version',
      minWidth: 80
    },
    {
      label: '数字版本号',
      prop: 'versionNum',
      minWidth: 80
    },
    {
      label: 'app应用类型',
      prop: 'appType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SYSTEM_APP_TYPE, row.appType)
    },
    {
      label: '升级更新时间',
      prop: 'upgradeTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '更新包',
      file: true,
      prop: 'url',
      minWidth: 120,
    },
    {
      label: '版本说明',
      prop: 'content',
      minWidth: 80,
      tooltip: true
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 80
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: 'app版本管理',
    items: [

      {
        component: 'input',
        label: '版本号',
        prop: 'version',
        placeholder: '请输入版本号如 v1.0.0',
        rules: [{required: true, message: '版本号不能为空', trigger: 'blur'}]
      },
      {
        component: 'number',
        label: '数字版本号',
        prop: 'versionNum',
        placeholder: '请输入数字版本号如 1001',
        rules: [{required: true, message: '数字版本号不能为空', trigger: 'blur'}]
      },
      {
        component: 'input',
        label: '版本名称',
        prop: 'name',
        placeholder: '请输入版本名称',
        rules: [{required: true, message: '版本名称不能为空', trigger: 'blur'}]
      },
      {
        component: 'select',
        label: 'app应用类型',
        prop: 'appType',
        placeholder: '请选择app应用类型',
        options: getStrDictOptions(DICT_TYPE.SYSTEM_APP_TYPE),
        rules: [{
          required: true,
          message: 'app应用类型不能为空',
          trigger: 'change'
        }]
      },
      {
        component: 'textarea',
        label: '版本说明',
        prop: 'content',
        placeholder: '请输入版本说明',
        showWordLimit: true,
        maxlength: 500,
        rules: [{required: true, message: '版本说明不能为空', trigger: 'blur'}]
      },
      {
        component: 'upload-file',
        label: '更新包',
        prop: 'url',
        allPermission: false,
        multiple: false,
        fileSize: 100,
        limit: 1,
        fileType: ['apk'],
        rules: [{ required: true, message: '更新包不能为空', trigger: 'blur' }]
      },
      {
        component: 'datePicker',
        label: '升级更新时间',
        prop: 'upgradeTime',
        placeholder: '选择升级更新时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
        rules: [{
          required: true,
          message: 'app应用类型不能为空',
          trigger: 'change'
        }]
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
      },
    ]
  }
])
