import request from '@/config/axios'
// 代码生成时其他模块调用
// 用户信息 VO
export interface UsersVO {
  id: number // 用户ID
  username: string // 用户账号
  password: string // 密码
  nickname: string // 用户昵称
  remark: string // 备注
  deptId: number // 部门ID
  postIds: string // 岗位编号数组
  email: string // 用户邮箱
  mobile: string // 手机号码
  sex: number // 用户性别
  avatar: string // 头像地址
  status: number // 帐号状态（0正常 1停用）
  loginIp: string // 最后登录IP
  loginDate: Date // 最后登录时间
}

// 用户信息 API
export const UsersApi = {
  // 查询用户信息分页
  getUsersPage: async (params: any) => {
    return await request.get({ url: `/system/user/page`, params })
  },
  // 查询用户信息列表
  getUsersList: async (params) => {
    return await request.get({ url: `/system/user/simple-list`, params })
  },

  // 查询用户信息详情
  getUsers: async (params: any) => {
    return await request.get({ url: `/system/user/get`, params })
  },

}
