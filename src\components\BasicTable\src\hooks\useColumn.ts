import { ComputedRef } from 'vue'
import { isFunction } from '@/utils/is'

export function useColumns(propsRef: ComputedRef<any>) {
  const propColumnsRef = ref([])
  const setColumns = (info: any) => {
    propColumnsRef.value = info
  }

  const getColumns = () => {
    return unref(propColumnsRef)
  }

  const summaryMethod = (data: any) => {
    if (unref(propsRef).summaryMethod && isFunction(unref(propsRef).summaryMethod)) {
      return unref(propsRef).summaryMethod(data)
    }
  }

  watch(
    () => propsRef.value,
    (v) => {
      propColumnsRef.value = v.columns
    }
  )

  return {
    propColumnsRef,
    setColumns,
    getColumns,
    summaryMethod
  }
}
