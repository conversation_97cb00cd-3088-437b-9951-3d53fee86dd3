<template>
  <div v-for="(config, index) in innerFormConfig" :key="index">
    <el-descriptions :title="config.title" direction="vertical" :column="2" border>
      <el-descriptions-item
        v-for="(item, index) in config.items"
        :key="index"
        :label="item.label"
        :span="item.span || 1"
        width="300"
      >
        <div v-if="item.files?.length > 0">
          <div
            v-for="(file, subIndex) in item.files"
            :key="subIndex"
            class="value text-blue-500 cursor-pointer"
            style="width: fit-content"
            @click="handleFile(file)"
          >
            {{ file.name }}
          </div>
        </div>
        <span v-else class="value">{{ item.value }}</span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts" name="BasicFormDetail">
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  formConfig: propTypes.array.def([])
})

const innerFormConfig: any = props.formConfig

const handleFile = (item: any) => {
  window.open(item.url)
}
</script>
