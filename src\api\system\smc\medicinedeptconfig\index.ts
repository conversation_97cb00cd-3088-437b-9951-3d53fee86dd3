import request from '@/config/axios'

// 药品基数配置 VO
export interface MedicineDeptConfigVO {
  id: number // 主键id
  deptId: number // 部门ID
  medicineId: number // 药品ID
  baseQuantity: number // 基数数量
  estimatedDays: number // 预计使用天数
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 药品基数配置 API
export const MedicineDeptConfigApi = {
  // 查询药品基数配置分页
  getMedicineDeptConfigPage: async (params: any) => {
    return await request.get({ url: `/system/medicine-dept-config/page`, params })
  },
  // 查询药品基数配置列表
  getMedicineDeptConfigList: async (params) => {
    return await request.get({ url: `/system/medicine-dept-config/list`, params })
  },

  // 查询药品基数配置详情
  getMedicineDeptConfig: async (params: any) => {
    return await request.get({ url: `/system/medicine-dept-config/get`, params })
  },

  // 新增药品基数配置
  createMedicineDeptConfig: async (data: MedicineDeptConfigVO) => {
    return await request.post({ url: `/system/medicine-dept-config/create`, data })
  },

  // 修改药品基数配置
  updateMedicineDeptConfig: async (data: MedicineDeptConfigVO) => {
    return await request.put({ url: `/system/medicine-dept-config/update`, data })
  },

  // 删除药品基数配置
  deleteMedicineDeptConfig: async (id: number) => {
    return await request.delete({ url: `/system/medicine-dept-config/delete?id=` + id })
  },

  // 导出药品基数配置 Excel
  exportMedicineDeptConfig: async (params) => {
    return await request.download({ url: `/system/medicine-dept-config/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/medicine-dept-config/get-import-template` })
  },

  // 导入药品基数配置 Excel
  importMedicineDeptConfig: async (formData) => {
    return await request.upload({ url: `/system/medicine-dept-config/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/medicine-dept-config/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },
}
