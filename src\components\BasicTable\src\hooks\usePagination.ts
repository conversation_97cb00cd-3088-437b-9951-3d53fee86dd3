import type { ComputedRef } from 'vue'
import { ref, unref, computed } from 'vue'
import { isBoolean } from '@/utils/is'

export function usePagination(refProps: ComputedRef<any>) {
  const configRef = ref<any>({})
  const show = ref(true)

  watch(
    () => unref(refProps).pagination,
    (pagination) => {
      if (!isBoolean(pagination) && pagination) {
        configRef.value = {
          ...unref(configRef),
          ...(pagination ?? {})
        }
      }
    }
  )

  const getPaginationInfo = computed((): any | boolean => {
    const { pagination } = unref(refProps)

    if (isBoolean(pagination) && !pagination) {
      return false
    }

    return {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 20, 30, 40],
      ...unref(configRef)
    }
  })

  function setPagination(info: Partial<any>) {
    const paginationInfo = unref(getPaginationInfo)
    configRef.value = {
      ...(!isBoolean(paginationInfo) ? paginationInfo : {}),
      ...info
    }
  }

  function getPagination() {
    return unref(getPaginationInfo)
  }

  function getShowPagination() {
    return unref(show)
  }

  async function setShowPagination(flag: boolean) {
    show.value = flag
  }

  return { getPagination, getPaginationInfo, setShowPagination, getShowPagination, setPagination }
}
