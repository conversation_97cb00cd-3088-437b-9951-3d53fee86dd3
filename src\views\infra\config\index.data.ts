import { DICT_TYPE, getBoolDictOptions, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '参数名称',
      prop: 'name'
    },
    {
      component: 'input',
      label: '参数键名',
      prop: 'key'
    },
    {
      component: 'select',
      label: '系统内置',
      prop: 'type',
      options: getIntDictOptions(DICT_TYPE.INFRA_CONFIG_TYPE)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '创建时间开始',
      endPlaceholder: '创建时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '参数主键', prop: 'id', minWidth: 60 },
    { label: '参数分类', prop: 'category', minWidth: 120, tooltip: true },
    { label: '参数名称', prop: 'name', minWidth: 180, tooltip: true },
    { label: '参数键名', prop: 'key', minWidth: 180, tooltip: true },
    { label: '参数键值', slot: 'value', minWidth: 180, tooltip: true },
    {
      label: '是否可见',
      prop: 'visible',
      minWidth: 120,
      dictType: DICT_TYPE.INFRA_BOOLEAN_STRING
    },
    {
      label: '系统内置',
      prop: 'type',
      minWidth: 120,
      dictType: DICT_TYPE.INFRA_CONFIG_TYPE
    },
    { label: '备注', minWidth: 180, prop: 'remark' },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '参数分类',
        prop: 'category',
        rules: [{ required: true, message: '参数分类不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '参数名称',
        prop: 'name',
        rules: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '参数键名',
        prop: 'key',
        rules: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '参数键值',
        prop: 'value',
        rules: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '是否可见',
        prop: 'visible',
        options: getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING),
        rules: [{ required: true, message: '是否可见不能为空', trigger: 'blur' }]
      },
      {
        component: 'textarea',
        label: '备注',
        prop: 'remark'
      }
    ]
  }
])
