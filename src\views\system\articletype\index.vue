<template>
  <BasicPage :tabs="['新闻资讯类型管理']">
    <template #action>
      <BasicButtonImport
              perm-code="system:article-type:import"
              file-name="新闻资讯类型管理"
              :template-api="ArticleTypeApi.importTemplate"
              :import-api="ArticleTypeApi.importArticleType"
              :exportError-file-api="ArticleTypeApi.exportErrorFile"
              @success="handlerImportSuccess"
      />
      <BasicButtonExport
              perm-code="system:article-type:export"
              file-name="新闻资讯类型管理"
              :export-api="ArticleTypeApi.exportArticleType"
      />
      <el-button v-hasPermi="['system:article-type:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register"/>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { ArticleTypeApi} from '@/api/system/articletype'
import { handleTree } from '@/utils/tree'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:article-type:update', callback: handleEdit },
    { label: '查看', permCode: 'system:article-type:query', callback: handleDetail },
    { label: '删除', permCode: 'system:article-type:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: ArticleTypeApi.getArticleTypeList,
  columns: totalColumns,
  rowKey: 'id',
  pagination: false,
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
      const list = handleTree(e)
    console.log(list)
    return list
  }
})

const searchFn = (e: any) => {
  reload()
}

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: ArticleTypeApi.getArticleType,
    submitApi: id ? ArticleTypeApi.updateArticleType : ArticleTypeApi.createArticleType
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await ArticleTypeApi.deleteArticleType(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>
