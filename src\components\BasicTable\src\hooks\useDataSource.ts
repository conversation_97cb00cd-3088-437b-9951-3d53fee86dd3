// import { useTimeoutFn } from '@/hooks/core/useTimeout';
import { isBoolean, isFunction } from '@/utils/is'
import { get, merge } from 'lodash-es'
import { ComputedRef, onMounted } from 'vue'

const tryOnUnmounted = (fn: () => any) => {
  if (getCurrentInstance()) onUnmounted(fn)
}

const useTimeoutRef = (wait: number) => {
  const readyRef = ref(false)
  let timer: TimeoutHandle
  function stop(): void {
    readyRef.value = false
    timer && window.clearTimeout(timer)
  }
  function start(): void {
    stop()
    timer = setTimeout(() => {
      readyRef.value = true
    }, wait)
  }

  start()

  tryOnUnmounted(stop)

  return { readyRef, stop, start }
}

const useTimeoutFn = (handle: Fn<any>, wait: number, native = false) => {
  if (!isFunction(handle)) {
    throw new Error('handle is not Function!')
  }

  const { readyRef, stop, start } = useTimeoutRef(wait)
  if (native) {
    handle()
  } else {
    watch(
      readyRef,
      (maturity) => {
        maturity && handle()
      },
      { immediate: false }
    )
  }
  return { readyRef, stop, start }
}

export function useDataSource(
  propsRef: ComputedRef<any>,
  { getPaginationInfo, setPagination, setLoading, clearSelectedRowKeys, tableData }: any,
  emit: any
) {
  const rawDataSourceRef = ref({})
  const dataSourceRef = ref([])

  watchEffect(() => {
    tableData.value = unref(dataSourceRef)
  })

  watch(
    () => unref(propsRef).data,
    () => {
      const { data, api } = unref(propsRef)
      !api && data && (dataSourceRef.value = data)
    },
    {
      immediate: true
    }
  )
  watch(
    () => unref(dataSourceRef),
    () => {
      emit('data-source-change', unref(dataSourceRef))
    },
    { deep: true }
  )

  // 监听分页变更
  function handlePageChange(current: number, size: number) {
    const { clearSelectOnPageChange } = unref(propsRef)
    if (clearSelectOnPageChange) {
      clearSelectedRowKeys()
    }
    setPagination({
      pageNo: current,
      pageSize: size
    })
    fetch()
  }

  function setData(data: any) {
    dataSourceRef.value = data
  }

  // 请求表格数据
  async function fetch(opt?: any): Promise<any> {
    const { api, beforeFetch, afterFetch, pagination } = unref(propsRef)

    if (!api || !isFunction(api)) return
    try {
      // 开始请求变更表格加载状态
      setLoading(true)

      let pageParams: Record<string, any> = {}
      const { pageNo = 1, pageSize } = unref(getPaginationInfo)

      if ((isBoolean(pagination) && !pagination) || isBoolean(getPaginationInfo)) {
        pageParams = {}
      } else {
        pageParams.pageNo = (opt && opt.page) || pageNo
        pageParams.pageSize = pageSize
      }

      let params: Record<string, any> | boolean = merge(pageParams)

      if (beforeFetch && isFunction(beforeFetch)) {
        params = (await beforeFetch(params)) ?? params
      }
      if (params === false) return

      const res = await api(params)
      rawDataSourceRef.value = res

      const isArrayResult = Array.isArray(res)

      let resultItems: any = isArrayResult ? res : get(res, 'list')
      const resultTotal: number | string = isArrayResult ? res.length : get(res, 'total')

      // 假如数据变少，导致总页数变少并小于当前选中页码，通过getPaginationRef获取到的页码是不正确的，需获取正确的页码再次执行
      if (Number(resultTotal)) {
        const currentTotalPage = Math.ceil(Number(resultTotal) / pageSize)
        if (pageNo > currentTotalPage) {
          setPagination({
            pageNo: currentTotalPage
          })
          return await fetch(opt)
        }
      }

      if (afterFetch && isFunction(afterFetch)) {
        resultItems = (await afterFetch(resultItems)) || resultItems
      }

      dataSourceRef.value = resultItems
      setPagination({
        total: resultTotal || 0
      })
      if (opt && opt.page) {
        setPagination({
          pageNo: opt.page || 1
        })
      }

      emit('fetch-success', {
        items: unref(resultItems),
        total: resultTotal
      })

      return resultItems
    } catch (err) {
      emit('fetch-error', err)
      dataSourceRef.value = []
      setPagination({
        total: 0
      })
    } finally {
      setLoading(false)
    }
  }

  // 重载表格数据
  async function reload(opt?: any) {
    return await fetch(opt)
  }

  function setApi(api: any) {
    propsRef.value.api = api
  }

  onMounted(() => {
    useTimeoutFn(() => {
      unref(propsRef).immediate && fetch()
    }, 16)
  })

  return {
    dataSourceRef,
    fetch,
    reload,
    setApi,
    setData,
    handlePageChange
  }
}
