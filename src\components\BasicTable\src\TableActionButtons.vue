<template>
  <el-button
    v-for="(btn, index) in normalButtons"
    :key="index"
    text
    @click="buttonEvent(btn.eventName)"
  >
    <span :style="`color: ${btn.color} !important`">{{ btn.label }}</span>
  </el-button>
  <el-dropdown v-if="moreButtons.length > 0">
    <div class="more">
      <Icon icon="ep:more-filled" :size="18" class="mt-4px" />
    </div>
    <template #dropdown>
      <el-dropdown-menu class="table-dropdown-menu">
        <el-dropdown-item
          v-for="(btn, index) in moreButtons"
          :key="index"
          @click="buttonEvent(btn.eventName)"
        >
          <span :style="`color: ${btn.color} !important`">{{ btn.label }}</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script lang="ts" setup name="TableActionButtons">
import { propTypes } from '@/utils/propTypes'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

const emit = defineEmits(['event'])

const props = defineProps({
  buttons: propTypes.array.def([]),
  row: propTypes.any.def(null) // 当前行数据
})

const normalButtons: any = ref([])
const moreButtons: any = ref([])

const handleButtons = () => {
  const permissions = wsCache.get(CACHE_KEY.USER).permissions
  const list = props.buttons.filter((item: any) => {
    const hasPermissions = permissions.some(
      (permission: string) => permission.includes(item.permCode) || !item.permCode
    )
    return hasPermissions && (item.show || item.show === undefined)
  })
  if (list.length <= 3) {
    normalButtons.value = list
    moreButtons.value = []
  } else {
    normalButtons.value = list.slice(0, 2)
    moreButtons.value = list.slice(2, list.length)
  }
}

const buttonEvent = (type: string) => {
  emit('event', type, props.row)
}

watch(
  () => props.buttons,
  () => {
    handleButtons()
  },
  { immediate: true }
)
</script>
