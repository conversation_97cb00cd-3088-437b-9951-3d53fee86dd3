<template>
  <BasicPage :tabs="['配置管理']">
    <template #action>
      <BasicButtonExport
        perm-code="pmis:project-control-price:export"
        file-name="配置信息"
        :params="{ ...searchForm }"
        :export-api="ConfigApi.exportConfig"
      />
      <el-button v-hasPermi="['infra:config:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register">
      <template #users="{ row }">
        <span v-for="userId in row.userIds" :key="userId" class="pr-5px">
          {{ userList.find((user) => user.id === userId)?.nickname }}
        </span>
      </template>
      <template #value="{ row }">
        <el-image
          v-if="row.configValueType == 'img'"
          :src="row.value.split(';')[0]"
          style="width: 60px; height: 60px"
          fit="cover"
          class="h-30px w-30px"
          preview-teleported
          :preview-src-list="row.value.split(';')"/>
        <div v-else-if="row.configValueType == 'editor'" style="cursor: pointer;color: #1890ff" @click="openForm('update', row.id)">
          点击查看
        </div>
        <div v-else>{{row.value}}</div>
      </template>
    </BasicTable>

<!--    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()"/>-->
  </BasicPage>
  <!-- 表单弹窗：添加/修改 -->
  <ConfigForm ref="formRef" @success="reload" />
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import ConfigForm from './ConfigForm.vue'
import * as ConfigApi from '@/api/infra/config'

defineOptions({ name: 'InfraConfig' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const userList: any = ref([]) // 用户列表

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '编辑', permCode: 'infra:config:update', callback: handleEdit },
    {
      label: '删除',
      permCode: 'infra:config:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api:ConfigApi.getConfigPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = (id: number | null, type: string) => {
  if (type == 'add') {
    formData.value.visible = true
  }
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: ConfigApi.getConfig,
    submitApi: id ? ConfigApi.updateConfig : ConfigApi.createConfig,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
function handleEdit({ id }) {
  // openFormDialog(id, id ? 'edit' : 'add')
  openForm(id ? 'update' : 'create',id)
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await ConfigApi.deleteConfig(id)
  message.success(t('common.delSuccess'))
  reload()
}


</script>

<style></style>
