<template>
  <BasicPage :tabs="['流程定义']">
    <BasicTable @register="register">
      <template #name="{ row }">
        <el-button type="primary" link @click="handleBpmnDetail(row)">
          <span>{{ row.name }}</span>
        </el-button>
      </template>
      <template #formType="{ row }">
        <el-button v-if="row.formType === 10" type="primary" link @click="handleFormDetail(row)">
          <span>{{ row.formName }}</span>
        </el-button>
        <el-button v-else type="primary" link @click="handleFormDetail(row)">
          <span>{{ row.formCustomCreatePath }}</span>
        </el-button>
      </template>
      <template #version="{ row }">
        <el-tag v-if="row">v{{ row.version }}</el-tag>
        <el-tag type="warning" v-else>未部署</el-tag>
      </template>
      <template #suspensionState="{ row }">
        <el-tag type="success" v-if="row.suspensionState === 1">激活</el-tag>
        <el-tag type="warning" v-if="row.suspensionState === 2">挂起</el-tag>
      </template>
    </BasicTable>
  </BasicPage>
  <!-- 弹窗：表单详情 -->
  <Dialog title="表单详情" v-model="formDetailVisible" width="800">
    <form-create :rule="formDetailPreview.rule" :option="formDetailPreview.option" />
  </Dialog>

  <!-- 弹窗：流程模型图的预览 -->
  <Dialog title="流程图" v-model="bpmnDetailVisible" width="800">
    <MyProcessViewer key="designer" v-model="bpmnXml" :value="bpmnXml as any" v-bind="bpmnControlForm" :prefix="bpmnControlForm.prefix" />
  </Dialog>
</template>

<script setup lang="ts">
import { tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { MyProcessViewer } from '@/components/bpmnProcessDesigner/package'
import * as DefinitionApi from '@/api/bpm/definition'
import { setConfAndFields2 } from '@/utils/formCreate'

defineOptions({ name: 'BpmProcessDefinition' })

const { push } = useRouter() // 路由
const { query } = useRoute() // 查询参数

const [register] = useTable({
  api: DefinitionApi.getProcessDefinitionPage,
  columns: tableData.value.columns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    return { ...params, key: query.key }
  },
  afterFetch: () => {}
})

/** 流程表单的详情按钮操作 */
const formDetailVisible = ref(false)
const formDetailPreview = ref({
  rule: [],
  option: {}
})
const handleFormDetail = async (row) => {
  if (row.formType == 10) {
    // 设置表单
    setConfAndFields2(formDetailPreview, row.formConf, row.formFields)
    // 弹窗打开
    formDetailVisible.value = true
  } else {
    await push({
      path: row.formCustomCreatePath
    })
  }
}

/** 流程图的详情按钮操作 */
const bpmnDetailVisible = ref(false)
const bpmnXml = ref(null)
const bpmnControlForm = ref({
  prefix: 'flowable'
})
const handleBpmnDetail = async (row) => {
  bpmnXml.value = (await DefinitionApi.getProcessDefinition(row.id))?.bpmnXml
  bpmnDetailVisible.value = true
}
</script>

<style></style>
