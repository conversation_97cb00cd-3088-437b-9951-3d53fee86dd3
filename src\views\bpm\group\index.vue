<template>
  <BasicPage :tabs="['用户分组']">
    <template #action>
      <el-button v-hasPermi="['bpm:user-group:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register">
      <template #users="{ row }">
        <span v-for="userId in row.userIds" :key="userId" class="pr-5px">
          {{ userList.find((user) => user.id === userId)?.nickname }}
        </span>
      </template>
    </BasicTable>

    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()">
      <template #userIds>
        <BasicSelect
          v-model="formData.userIds"
          :options="userList"
          placeholder="请选择成员"
          filterable
          multiple
          :field-names="{ label: 'nickname', value: 'id' }"
        />
      </template>
    </BasicFormDrawer>
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as UserApi from '@/api/system/user'
import * as UserGroupApi from '@/api/bpm/userGroup'

defineOptions({ name: 'BpmUserGroup' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const userList: any = ref([]) // 用户列表

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '编辑', permCode: 'bpm:user-group:update', callback: handleEdit },
    {
      label: '删除',
      permCode: 'bpm:user-group:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: UserGroupApi.getUserGroupPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = (id: number | null, type: string) => {
  if (type == 'add') {
    formData.value.status = 0
  }
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: UserGroupApi.getUserGroup,
    submitApi: id ? UserGroupApi.updateUserGroup : UserGroupApi.createUserGroup,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await UserGroupApi.deleteUserGroup(id)
  message.success(t('common.delSuccess'))
  reload()
}

/** 初始化 **/
onMounted(async () => {
  // 加载用户列表
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style></style>
