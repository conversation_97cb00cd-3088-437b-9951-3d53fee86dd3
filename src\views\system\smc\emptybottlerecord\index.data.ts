import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
  import {CabinetApi} from "@/api/system/smc/cabinet";
  import {MedicineApi} from "@/api/system/smc/medicine";
  import {DocumentsApi} from "@/api/system/smc/documents";
  import {DeptApi} from "@/api/system/dept/deptOption";

export const formSearchConfig = {
  itemList: [
    {
      component: 'select',
      label: '药柜ID',
      prop: 'cabinetId',
      placeholder: '请选择药柜ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: CabinetApi.getCabinetList,
    },
    {
      component: 'select',
      label: '药品ID',
      prop: 'medicineId',
      placeholder: '请选择药品ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: MedicineApi.getMedicineList,
    },
    {
      component: 'select',
      label: '医生取(分)药品批次关联ID',
      prop: 'documentDoctorMedicineId',
      placeholder: '请选择医生取(分)药品批次关联ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: DocumentsApi.getDocumentsList,
    },
    {
      component: 'input',
      label: '批次号',
      prop: 'batchNumber',
      placeholder: '请输入批次号'
    },
    {
      component: 'datePickerRange',
      label: '回收日期',
      prop: 'recycleDate',
      startPlaceholder: '回收日期开始日期',
      endPlaceholder: '回收日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'treeSelect',
      label: '部门ID',
      prop: 'deptId',
      placeholder: '请选择部门ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: DeptApi.getDeptList,
    },
    {
      component: 'input',
      label: '医生ID',
      prop: 'doctorId',
      placeholder: '请输入医生ID'
    },
    {
      component: 'input',
      label: '回收操作人',
      prop: 'recycleOperator',
      placeholder: '请输入回收操作人'
    },
    {
      component: 'input',
      label: '取出操作人',
      prop: 'collectOperator',
      placeholder: '请输入取出操作人'
    },
    {
      component: 'datePickerRange',
      label: '取出时间',
      prop: 'collectTime',
      startPlaceholder: '取出时间开始日期',
      endPlaceholder: '取出时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'number',
      label: '待回收数量',
      prop: 'pendingRecycle',
      placeholder: '请输入待回收数量'
    },
    {
      component: 'number',
      label: '可取空瓶数量',
      prop: 'availableCollect',
      placeholder: '请输入可取空瓶数量'
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药柜ID',
      prop: 'cabinetIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品ID',
      prop: 'medicineIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '医生取(分)药品批次关联ID',
      prop: 'documentDoctorMedicineIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '批次号',
      prop: 'batchNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '当日取药数量',
      prop: 'takenQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '使用数量',
      prop: 'usedQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '已退回药品数量',
      prop: 'returnedQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '已回收空瓶数量',
      prop: 'recycledQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '已取出空瓶数量',
      prop: 'collectedQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '回收日期',
      prop: 'recycleDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '部门ID',
      prop: 'deptIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '医生ID',
      prop: 'doctorId',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '回收操作人',
      prop: 'recycleOperator',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '取出操作人',
      prop: 'collectOperator',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '取出时间',
      prop: 'collectTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '待回收数量',
      prop: 'pendingRecycle',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '可取空瓶数量',
      prop: 'availableCollect',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '空瓶回收记录',
    items: [
      {
        component: 'select',
        label: '药柜ID',
        prop: 'cabinetId',
        placeholder: '请选择药柜ID',
        params: {},
        filterable: true,
        propName: 'cabinetIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: CabinetApi.getCabinetList,
        rules: [{ required: true, message: '药柜ID不能为空', trigger: 'change' }]
      },
      {
        component: 'select',
        label: '药品ID',
        prop: 'medicineId',
        placeholder: '请选择药品ID',
        params: {},
        filterable: true,
        propName: 'medicineIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: MedicineApi.getMedicineList,
        rules: [{ required: true, message: '药品ID不能为空', trigger: 'change' }]
      },
      {
        component: 'select',
        label: '医生取(分)药品批次关联ID',
        prop: 'documentDoctorMedicineId',
        placeholder: '请选择医生取(分)药品批次关联ID',
        params: {},
        filterable: true,
        propName: 'documentDoctorMedicineIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: DocumentsApi.getDocumentsList,
      },
      {
        component: 'input',
        label: '批次号',
        prop: 'batchNumber',
        placeholder: '请输入批次号',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '当日取药数量',
        prop: 'takenQuantity',
        max: 9999999999,
        placeholder: '请输入当日取药数量',
          rules: [{ required: true, message: '当日取药数量不能为空', trigger: 'blur' }]
      },
      {
        component: 'number',
        label: '使用数量',
        prop: 'usedQuantity',
        max: 9999999999,
        placeholder: '请输入使用数量',
          rules: [{ required: true, message: '使用数量不能为空', trigger: 'blur' }]
      },
      {
        component: 'number',
        label: '已退回药品数量',
        prop: 'returnedQuantity',
        max: 9999999999,
        placeholder: '请输入已退回药品数量',
      },
      {
        component: 'number',
        label: '已回收空瓶数量',
        prop: 'recycledQuantity',
        max: 9999999999,
        placeholder: '请输入已回收空瓶数量',
      },
      {
        component: 'number',
        label: '已取出空瓶数量',
        prop: 'collectedQuantity',
        max: 9999999999,
        placeholder: '请输入已取出空瓶数量',
      },
      {
        component: 'datePicker',
        label: '回收日期',
        prop: 'recycleDate',
        placeholder: '选择回收日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
          rules: [{ required: true, message: '回收日期不能为空', trigger: 'blur' }]
      },
      {
        component: 'treeSelect',
        label: '部门ID',
        prop: 'deptId',
        placeholder: '请选择部门ID',
        filterable: true,
        params: {},
        propName: 'deptIdName',
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        api: DeptApi.getDeptList,
        rules: [{ required: true, message: '部门ID不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '医生ID',
        prop: 'doctorId',
        placeholder: '请输入医生ID',
        rules: [
              { required: true, message: '医生ID不能为空', trigger: 'blur' },
              { max: 19, message: '长度不能超过19个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '回收操作人',
        prop: 'recycleOperator',
        placeholder: '请输入回收操作人',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '取出操作人',
        prop: 'collectOperator',
        placeholder: '请输入取出操作人',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'datePicker',
        label: '取出时间',
        prop: 'collectTime',
        placeholder: '选择取出时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'number',
        label: '待回收数量',
        prop: 'pendingRecycle',
        max: 9999999999,
        placeholder: '请输入待回收数量',
      },
      {
        component: 'number',
        label: '可取空瓶数量',
        prop: 'availableCollect',
        max: 9999999999,
        placeholder: '请输入可取空瓶数量',
      },
      {
        component: 'number',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        max: 999,
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
    ]
  }
])
