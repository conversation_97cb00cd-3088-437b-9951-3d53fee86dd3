<template>
  <BasicDialog v-model:show="innerVisible" :title="headerTitle" :width="width"  :show-footer="!isView" @close="handleClose">
    <template #content>
      <div v-loading="loading">
        <BasicFormSubmit
          ref="formRef"
          :obj-list="innerOptions.formConfigList"
          :data="innerOptions.formData"
          label-position="top"
          label-width="100px"
          :is-view="isView"
        >
          <template #sub-slot="{ propName }">
            <slot :name="propName"></slot>
          </template>
        </BasicFormSubmit>
      </div>
    </template>
    <template #action>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="handleConfirm(false)">确定</el-button>
    </template>
  </BasicDialog>
</template>

<script lang="ts" setup name="BasicFormDialog">
// import { propTypes } from '@/utils/propTypes'
import { isObject } from '@/utils/is'
import { throttle } from 'lodash-es'
import {handleTree} from "@/utils/tree";

const message = useMessage()

const emit = defineEmits(['refresh'])

interface OptionsType {
  title: string // 标题
  width: string // 宽度
  type: string // 表单类型 add新增 edit编辑 detail详情
  successMsg: string // 成功提示信息 none不显示提示信息
  formConfigList: any // 表单配置
  formData: any // 表单数据
  queryApi: Function // 查询接口
  queryParams?: Object // 查询参数
  submitApi: Function // 提交接口
  afterFetch?: Function // 查询成功后回调
  beforeSubmit?: Function // 提交前回调
  afterSubmit?: Function // 提交成功后回调
  onClose?: Function // 关闭回调
  change?: Function // 表单数据变化回调
}

const innerVisible = ref(false)
const headerTitle = ref('')
const width = ref('50%')
const loading = ref(false)
const btnLoading = ref(false)
const formRef = ref()
const isView = ref(false)

const innerOptions = ref<OptionsType>({
  title: '', // 标题
  width: '', // 宽度
  type: '', // 表单类型 add新增 edit编辑 detail详情
  successMsg: '', // 成功提示信息,  none不显示提示信息
  formConfigList: [], // 表单配置
  formData: {}, // 表单数据
  queryApi: () => {}, // 查询接口
  queryParams: {}, // 查询参数
  submitApi: () => {}, // 提交接口
  afterFetch: () => {}, // 查询成功后回调
  beforeSubmit: () => {}, // 提交前回调
  afterSubmit: () => {}, // 提交成功后回调
  onClose: () => {}, // 关闭回调
  change: () => {} // 表单数据变化回调
})

const getTypeLabel = (type: string = '') => {
  switch (type) {
    case 'add':
      return '新增'
    case 'edit':
      return '编辑'
    case 'detail':
      return '详情'
    default:
      return ''
  }
}

/**
 *
 * @param id 表单id
 * @param type 表单类型 add新增 edit编辑 detail详情
 * @param options 表单配置参数
 */
const init = async (id: number | null, options: OptionsType) => {
  formRef.value?.resetForm()
  await dealSelectApiOptions(options);
  innerOptions.value = options
  // const titleType = id ? '编辑' : '新增'
  // headerTitle.value = `${options.title} ${titleType}`
  if (options.title) {
    headerTitle.value = options.title
  } else {
    headerTitle.value = getTypeLabel(options.type)
  }
  if(options.width) {
    width.value = options.width
  }
  isView.value = ['view', 'detail'].includes(options.type)

  innerVisible.value = true

  if (!innerOptions.value.queryApi) {
    if (options.afterFetch) {
      options.afterFetch(innerOptions.value.formData)
    }
    return
  }

  if ((id || options.queryParams) && options.type !== 'add') {
    loading.value = true
    const params: any = { id, ...options.queryParams }
    if (!id) {
      delete params.id
    }
    try {
      const res = await innerOptions.value.queryApi(params)
      innerOptions.value.formData = res || {}
      // 回调查询成功后
      if (options.afterFetch) {
        options.afterFetch(innerOptions.value.formData)
      }
    } finally {
      loading.value = false
    }
  }
}

async function dealSelectApiOptions(options) {
  if (!isView.value){
    for (let formConfigItem of options.formConfigList) {
      let filterItems = formConfigItem.items.filter(e =>  e.api && (e.component == 'select' || e.component == 'treeSelect'));
      for (let item of filterItems) {
        const res = await item.api(item.params)
        const tempList = res.map((option: any) => {
          const newItem = {}
          for (let key in item.fieldNames) {
            newItem[key] = option[item.fieldNames[key]]
          }
          return newItem
        })
        if(item.component == 'select'){
          item.options = tempList
        }
        if(item.component == 'treeSelect'){
          item.options = handleTree(tempList);
        }
      }
    }
  }
}

const handleConfirm = throttle(async (step = false, callback: any = null) => {
  const valid = await formRef.value?.submitForm()
  if (!valid) return false

  const refList = formRef.value.uploadFileFinishList()
  const unfinish = refList?.filter((i: any) => !i.uploadFinish) || []
  if (unfinish.length > 0) {
    message.warning('请等待文件上传完成')
    return
  }

  try {
    btnLoading.value = true
    const { formData, type, beforeSubmit, submitApi, afterSubmit, successMsg } = innerOptions.value
    // 提交表单数据
    // 回调提交前
    let pass = true
    if (beforeSubmit) {
      pass = await beforeSubmit(formData)
    }

    // 提交前外部如果返回false，则不提交
    if (pass === false) return

    let res: any = {}
    if (isObject(pass)) {
      res = await submitApi(pass)
    } else {
      res = await submitApi(formData)
      if (type === 'edit' && formData.id) {
        res = formData.id
      }
    }
    if (callback) {
      callback(res)
    }

    if (!step) {
      if (!res.errorList || res.errorList == 0) {
        emit('refresh')
        innerVisible.value = false
        if (successMsg !== 'none') {
          message.success(successMsg || '操作成功')
        }
      }

      setTimeout(() => {
        if (afterSubmit) {
          afterSubmit(res)
        }
      }, 400)
    }
  } finally {
    btnLoading.value = false
  }
}, 2000)

const handleClose = () => {
  formRef.value?.resetForm()
  if (innerOptions.value.onClose) {
    innerOptions.value.onClose()
  }
  innerVisible.value = false
}

const setFormConfig = (formConfig: any) => {
  innerOptions.value.formConfigList = formConfig
}

const close = () => {
  handleClose()
}

const submitAudit = throttle(async () => {
  let response: any // 可能是业务数据，也可能是boolean
  const valid = await handleConfirm(true, (data: any) => {
    response = data
  })

  if (valid === false) {
    response = false
  }
  return response
}, 2000)

watch(
  () => innerOptions.value.formData,
  () => {
    if (innerOptions.value.change) {
      innerOptions.value.change(innerOptions.value.formData)
    }
  },
  { deep: true }
)

defineExpose({ init, setFormConfig, close, submitAudit })
</script>
