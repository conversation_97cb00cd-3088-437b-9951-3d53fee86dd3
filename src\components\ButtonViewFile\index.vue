<template>
  <el-button link type="primary" @click="dialogVisible = true">{{ name }}</el-button>
  <BasicDialog v-model:show="dialogVisible" title="查看附件">
    <template #content>
      <el-table :data="tableData">
      <el-table-column type="selection" fixed="left" width="50" />
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column
        v-for="(item, index) in tableColumns"
        :key="index"
        :label="item.label"
        :prop="item.prop"
        :min-width="item.prop === 'col1' ? 200 : 100"
      >
        <template v-if="item.prop === 'col1'" #default="scope">
          <span class="text-blue-500" style="cursor: pointer" @click="handleFile(scope.row.col1)">
            {{ scope.row.col1.name }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    </template>
    <template #action>
      <el-button type="primary">下载</el-button>
    </template>
  </BasicDialog>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import { tableColumns, tableData } from './data'

defineOptions({ name: 'ButtonViewFile' })
const emit = defineEmits(['confirm'])
defineProps({
  name: propTypes.string.def('查看')
})

const dialogVisible = ref(false)

const open = async () => {
  dialogVisible.value = true
}

const handleFile = (item: any) => {
  window.open(item.url)
}

defineExpose({ open })
</script>
