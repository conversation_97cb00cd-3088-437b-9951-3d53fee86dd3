table {
  thead,
  tbody {
    tr {
      th,
      td {
        color: #303133 !important;
      }
      .selection-column .cell {
        min-width: 42px;
      }
    }
  }
  .table-header .cell {
    min-width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-table__cell .cell .slot-column {
    .el-button {
      &:nth-of-type(n + 2) {
        margin-left: 6px;
      }
    }
  }
}

// .el-loading-mask {
//   // margin-top: 40px;
//   margin-top: 0;
// }

.table-dropdown-menu {
  padding: 4px !important;
  .el-dropdown-menu__item {
    color: var(--el-color-primary);
    min-width: 50px !important;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
// 分页
// .el-pagination {
//   display: flex;
//   justify-content: flex-end;
//   align-items: center;
//   ::v-deep(tbody tr td:last-child .cell) {
//     padding-left: 0;
//     padding-right: 0;
//   }
//   ::v-deep(.el-input__inner) {
//     height: 22px !important;
//   }
//   ::v-deep(.el-input__suffix) {
//     top: 0px;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     flex-wrap: nowrap;
//     flex-direction: row;
//     align-content: flex-start;
//   }
//   ::v-deep(.el-input__icon) {
//     line-height: 32px;
//   }
//   ::v-deep(.el-pagination__sizes) {
//     margin-right: 0;
//   }
//   ::v-deep(.el-pagination__jump) {
//     margin-left: 0;
//   }
// }

// 选中效果
.el-table .selected-row-color {
  background-color: #e6f3ff !important;
}

.el-table--enable-row-hover .el-table__body tr.selected-row-color:hover > td {
  background-color: #e6f3ff !important;
}

.el-table__body-wrapper .el-table__body tr.selected-row-color.hover-row > td {
  background-color: #e6f3ff !important;
}

.el-table__fixed-body-wrapper .el-table__body tr.selected-row-color.hover-row > td {
  background-color: #e6f3ff !important;
}

// .el-table__body-wrapper tr.selected-row-color td.el-table-fixed-column--left {
//   background: #e6f3ff;
//   text-align: center !important;
// }
// .el-table__body-wrapper tr td.el-table-fixed-column--left {
//   text-align: center !important;
// }

.el-table__body-wrapper tr.selected-row-color td.el-table-fixed-column--right {
  background: #e6f3ff;
}
