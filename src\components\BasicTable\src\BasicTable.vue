<template>
  <div class="relative">
    <ColumnConfig v-if="getBindValue?.columnsSetting" :columns="configColumns" @update="updateColumns" />
    <el-table
      ref="tableRef"
      v-loading="getLoading"
      :row-key="innerPropsRef.rowKey"
      :style="tableStyle"
      :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
      :data="getBindValue.data"
      :row-class-name="setRowClass"
      :border="innerPropsRef.border"
      scrollbar-always-on
      header-cell-class-name="table-header"
      :highlight-current-row="innerPropsRef.highlightCurrentRow && !innerPropsRef.multiple"
      :row-style="rowStyle"
      :show-summary="innerPropsRef.showSummary"
      :summary-method="summaryMethod"
      @current-change="currentChange"
      @selection-change="selectionChange"
      @row-click="rowClick"
    >
      <template #empty>
        <div class="empty">
          <!-- <span class="label">{{ getLoading ? '加载中...' : '暂无数据' }}</span> -->
          <el-empty :description="getLoading ? '加载中...' : '暂无数据'" :image-size="80" />
        </div>
      </template>
      <template v-for="(column, idx) in getBindValue?.columns">
        <!-- 选择框 -->
        <el-table-column
          v-if="column?.selection && !column?.invisible"
          :key="'selection' + idx"
          type="selection"
          width="50"
          fixed="left"
          align="center"
          :selectable="column?.selectable"
          :reserve-selection="column?.reserveSelection"
          class-name="selection-column"
        />
        <!-- 序号 -->
        <el-table-column
          v-else-if="column?.index && !column?.invisible"
          :key="'index' + idx"
          type="index"
          :width="column.width || 80"
          fixed="left"
          :label="column?.label || '编号'"
          :index="column.filter"
        />

        <!-- html内容显示 -->
        <el-table-column
          v-else-if="column?.html && !column?.invisible"
          :key="'html' + idx"
          :label="column?.label"
          :prop="column?.prop"
          v-bind="column"
        >
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="handleGetValue(column, scope)"
              placement="top"
            >
              <div v-html="scope.row[column.prop]" style="white-space: pre-wrap;"></div>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- 内容link点击列 -->
        <el-table-column
          v-else-if="column?.link && !column?.invisible"
          :key="'link' + idx"
          :label="column?.label"
          :prop="column?.prop"
          v-bind="column"
        >
          <template #default="scope">
            <el-link v-if="checkPermi([column.link.permCode])" type="primary" @click="column.link.callback(Object.assign({}, scope.row), column.link.label)">
              <slot v-if="column?.slot" :name="column.slot ? column.slot : ''" v-bind="scope"></slot>
              <span v-else>
                {{ handleGetValue(column, scope) }}
              </span>
            </el-link>
            <span v-else>
              {{ handleGetValue(column, scope) }}
            </span>
          </template>
        </el-table-column>
        <!-- 操作栏 -->
        <el-table-column
          v-else-if="column?.operation && !column?.invisible"
          :key="'operation' + idx"
          :label="column?.label || '操作'"
          fixed="right"
          :width="column.width || 200"
          class-name="operation-column"
        >
          <template #default="scope">
            <div v-if="!column.buttons" class="operation">
              <slot name="operation" v-bind="scope"></slot>
            </div>
            <!-- 操作栏 按钮少于3个 -->
            <div v-else-if="column.buttons.length <= 3" class="operation">
              <div v-for="btn in column.buttons" :key="btn.label" class="operation-sub">
                <el-button
                  v-hasPermi="[btn.permCode]"
                  v-show="btn.hidden ? btn.hidden(scope.row) : true"
                  text
                  :style="`color: ${btn.color}`"
                  @click="btn.callback(Object.assign({}, scope.row), btn.label)"
                >
                  {{ btn.label }}
                </el-button>
              </div>
            </div>
            <!-- 操作栏 按钮大于3个 -->
            <div v-else class="operation">
              <el-button
                v-hasPermi="[column.buttons[0].permCode]"
                v-show="column.buttons[0].hidden ? column.buttons[0].hidden(scope.row) : true"
                text
                :style="`color: ${column.buttons[0].color}`"
                @click="column.buttons[0].callback(Object.assign({}, scope.row), column.buttons[0].label)"
              >
                {{ column.buttons[0].label }}
              </el-button>
              <el-button
                v-hasPermi="[column.buttons[1].permCode]"
                v-show="column.buttons[1].hidden ? column.buttons[1].hidden(scope.row) : true"
                text
                :style="`color: ${column.buttons[1].color}`"
                @click="column.buttons[1].callback(Object.assign({}, scope.row), column.buttons[1].label)"
              >
                {{ column.buttons[1].label }}
              </el-button>
              <el-dropdown>
                <div class="more">
                  <Icon icon="ep:more-filled" :size="18" class="mt-4px" />
                </div>
                <template #dropdown>
                  <el-dropdown-menu class="table-dropdown-menu">
                    <div v-for="btn in getMoreButtons(column.buttons)" :key="btn.label">
                      <el-dropdown-item v-hasPermi="[btn.permCode]" v-if="btn.hidden ? btn.hidden(scope.row) : true">
                        <span :style="`color: ${btn.color}`" @click="btn.callback(Object.assign({}, scope.row), btn.label)">
                          {{ btn.label }}
                        </span>
                      </el-dropdown-item>
                    </div>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>

        <!-- 字典Tag -->
        <el-table-column
          v-else-if="column?.dictType && !column?.invisible"
          :key="'dictTag' + idx"
          :label="column?.label"
          :prop="column?.prop"
          v-bind="column"
        >
          <template #default="scope">
            <span v-if="scope.row[column?.prop] === null || scope.row[column?.prop] === undefined || scope.row[column?.prop] === '--'">
              --
            </span>
            <dict-tag v-else :type="column?.dictType" :value="scope.row[column?.prop]" />
          </template>
        </el-table-column>

        <!-- 文件Tag -->
        <el-table-column
          v-else-if="column?.file && !column?.invisible"
          :key="'file' + idx"
          :label="column?.label"
          :prop="column?.prop"
          v-bind="column"
        >
          <template #default="scope">
            <BasicButtonViewFile :file-ids="scope.row[column?.prop]" />
          </template>
        </el-table-column>

        <!-- 图片 -->
        <el-table-column
          v-else-if="column?.image && !column?.invisible"
          :key="'image' + idx"
          :label="column?.label"
          :prop="column?.prop"
          v-bind="column"
        >
          <template #default="scope">
            <div style="position: relative;width: 50px;height: 50px;">
              <BasicImageView :inTable="true" :fit="'fill'" :file-ids="handleGetValue(column, scope)" />
            </div>
          </template>
        </el-table-column>

        <!-- 其他 :show-overflow-tooltip="column.tooltip" -->
        <el-table-column
          v-else-if="!column?.invisible"
          :key="idx"
          :label="column?.label"
          :prop="column?.prop"
          :show-overflow-tooltip="column?.tooltip"
          :sortable="column?.sortable || false"
          v-bind="column"
        >
          <template #default="scope">
            <span class="slot-column">
              <slot v-if="column?.slot" :name="column.slot ? column.slot : ''" v-bind="scope"></slot>
              <span v-else>
                {{ handleGetValue(column, scope) }}
              </span>
            </span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-row v-if="getPaginationInfo?.total > 0" justify="end" class="mt-14px">
      <el-pagination
        :current-page="getPaginationInfo.pageNum"
        :page-size="getPaginationInfo.pageSize"
        background
        layout="total, prev, pager, next, sizes"
        :page-sizes="getPaginationInfo.pageSizes"
        :total="Number(getPaginationInfo.total)"
        @size-change="sizeChange"
        @current-change="pageChange"
      />
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { useColumns } from './hooks/useColumn'
import { useDataSource } from './hooks/useDataSource'
import { useLoading } from './hooks/useLoading'
import { usePagination } from './hooks/usePagination'
import { useSelection } from './hooks/useSelection'
import { tableProps } from './props'
import ColumnConfig from './ColumnConfig.vue'
import { checkPermi } from '@/utils/permission'

defineOptions({ name: 'BasicTable' })

const tableData = ref<Record<string, any>[]>([])
const innerPropsRef = ref<Partial<any>>({})

const props = defineProps(tableProps)

const emit = defineEmits(['fetch-success', 'fetch-error', 'register', 'data-source-change'])

// 注册表格组件后设置、获取属性事件
const setProps = (props: Partial<any>) => {
  innerPropsRef.value = { ...unref(innerPropsRef), ...props }
}
const getProps = computed(() => {
  return {
    ...props,
    ...unref(innerPropsRef)
  }
})

const tableStyle = computed(() => {
  return {
    width: '100%',
    // height: 'calc(100% - 96px - 30px)',
    minHeight: '160px'
  }
})

// 分页hook相关
const { setLoading, getLoading } = useLoading(getProps)
const { getPaginationInfo, getPagination, setPagination, setShowPagination, getShowPagination } = usePagination(getProps)
const sizeChange = (val: any) => {
  setPagination({
    pageNum: getPagination().pageNum,
    pageSize: val
  })
  handlePageChange.call(undefined, getPagination().pageNum, getPagination().pageSize)
}
const pageChange = (val: any) => {
  setPagination({
    pageNum: val,
    pageSize: getPagination().pageSize
  })
  handlePageChange.call(undefined, getPagination().pageNum, getPagination().pageSize)
}

// 字段值设置
const { propColumnsRef, getColumns, setColumns, summaryMethod } = useColumns(getProps)

// 表格数据处理hook调用
const { dataSourceRef, fetch, reload, handlePageChange, setApi, setData } = useDataSource(
  getProps,
  {
    getPaginationInfo,
    setLoading,
    setPagination,
    tableData
  },
  emit
)

// 操作按钮获取
const getMoreButtons = (list: any) => {
  if (list.length > 2) {
    return list.slice(2, list.length)
  }
  return list
}
// 多选相关
const { selectedList, selectedListIds, selectionChange, currentChange, currentRow } = useSelection(getProps)
const setRowClass = (item: any) => {
  if (selectedListIds.value.includes(item.row[getBindValue?.value.rowKey ?? 'id'])) {
    return 'selected-row-color'
  }
  return ''
}

const getBindValue: any = computed(() => {
  return {
    ...unref(getProps),
    data: unref(dataSourceRef),
    columns: unref(propColumnsRef)
  }
})

const rowStyle = () => {
  return {
    cursor: innerPropsRef.value.highlightCurrentRow ? 'pointer' : 'default'
  }
}

const handleGetValue = (column: any, scope: any) => {
  if (column?.filter) {
    return column.filter(scope.row)
  }
  if (!scope.row[column?.prop] && column?.default) {
    return column.default
  }
  if (column?.prop.includes('.')) {
    const props = column?.prop.split('.')
    return props.reduce((pre: any, next: any) => pre?.[next], scope.row) || '--'
  }
  return scope.row[column?.prop] || '--'
}

const tableRef = ref<any>()
const rowClick = (row: any) => {
  if (innerPropsRef.value.multiple) {
    tableRef.value?.toggleRowSelection(row)
  }
}

const configColumns = ref([])
const updateColumns = (columns: any) => {
  setColumns(columns)
}

// 反显多个选中行
const setSelectedRows = (rows: any) => {
  if (innerPropsRef.value.multiple) {
    tableRef.value?.clearSelection()
    selectedList.value = rows
    if (rows.length === 0) return
    setTimeout(() => {
      selectedList.value.forEach((row: any) => {
        tableRef.value?.toggleRowSelection(row)
      })
    }, 0)
  }
}

const setCurrentRow = (info: any) => {
  if (!info) {
    tableRef.value?.setCurrentRow()
    return
  }
  const { rows, rowId } = info
  const rowKey = getProps?.value.rowKey ?? 'id'
  const currentRow = rows.find((i: any) => i[rowKey] === rowId)
  if (currentRow) {
    tableRef.value?.setCurrentRow(currentRow)
    return
  }
}

const updatePagination = (pagination: any) => {
  handlePageChange.call(undefined, pagination.pageNum, pagination.pageSize)
  setPagination(pagination)
}

onMounted(() => {
  configColumns.value = getColumns()
})

// 表格action事件
const tableAction = {
  reload,
  fetch,
  setApi,
  setProps,
  getProps,
  setLoading,
  emit,
  getPaginationRef: getPagination,
  updatePagination,
  setColumns,
  getColumns,
  setShowPagination,
  getShowPagination,
  selectionChange,
  currentChange,
  selectedListIds,
  selectedList,
  summaryMethod,
  currentRow,
  setCurrentRow,
  setSelectedRows,
  setData
}

emit('register', tableAction)
</script>

<style lang="scss" scoped>
:deep(.el-empty) {
  padding-top: 14px;
  padding-bottom: 0;
  .el-empty__description {
    margin-top: 0 !important;
  }
}
.operation {
  display: flex;
  align-items: center;
  .el-button,
  .operation-sub {
    margin-left: 0;
    margin-right: 6px;
  }
  :deep(.is-text) {
    color: var(--el-color-primary);
    font-weight: normal;
    background-color: transparent;
    padding: 0 4px !important;
    margin-left: 0;
    margin-right: 6px;
    &:hover {
      background-color: transparent;
    }
  }
  :deep(.more) {
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;
    margin-left: 2px;
    .el-icon {
      color: var(--el-color-primary);
    }
  }
}
.el-pagination {
  :deep(.el-input__wrapper) {
    background-color: rgb(242, 243, 245) !important;
    box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
    cursor: default;
    height: 32px !important;
    border: none !important;
    .el-input__inner {
      cursor: default !important;
      border-color: #dcdfe6 !important;
    }
  }
}

:deep(.el-popper) {
  max-width: 800px;
  max-height: 400px;
  overflow-y: auto;
}
</style>

<style>
.el-table--border::after,
.el-table--border::before,
.el-table--border .el-table__inner-wrapper::after,
.el-table__inner-wrapper::before {
  border-color: transparent !important;
  background-color: transparent !important;
}
.el-table__empty-text {
  width: 100% !important;
  border: 1px solid var(--el-border-color-lighter) !important;
}
.el-table__body tr.current-row > td {
  color: #191919 !important;
  font-weight: 500 !important;
  background: var(--el-color-primary-light-3) !important;
  transition: all 0.5s ease !important;
}
</style>
