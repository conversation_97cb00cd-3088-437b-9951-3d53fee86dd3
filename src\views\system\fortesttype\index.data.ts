import {
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
import {ForTestTypeApi} from "@/api/system/fortesttype";

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '名称',
      prop: 'name',
      placeholder: '请输入名称'
    },
    {
      component: 'select',
      label: '父类id',
      prop: 'parentId',
      placeholder: '请选择父类id',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: ForTestTypeApi.getForTestTypeList,
    },
    {
      component: 'input',
      label: '分类图标',
      prop: 'iconUrl',
      placeholder: '请输入分类图标'
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      minWidth: 80
    },
    {
      label: '名称',
      prop: 'name',
      minWidth: 80
    },
    {
      label: '父类id',
      prop: 'parentId',
      minWidth: 80
    },
    {
      label: '分类图标',
      prop: 'iconUrl',
      minWidth: 80
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 80
    },
    {
      label: '排序',
      prop: 'sort',
      minWidth: 80
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 80
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '测试而已的类型',
    items: [
      {
        component: 'input',
        label: '名称',
        prop: 'name',
        placeholder: '请输入名称',
        rules: [
              { required: true, message: '名称不能为空', trigger: 'blur' },
              { max: 64, message: '长度不能超过64个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '父类id',
        prop: 'parentId',
        placeholder: '请选择父类id',
        params: {},
        filterable: true,
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: ForTestTypeApi.getForTestTypeList,
      },
      {
        component: 'input',
        label: '分类图标',
        prop: 'iconUrl',
        placeholder: '请输入分类图标',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        max: 999,
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
    ]
  }
])
