<template>
  <BasicPage :tabs="['测试而已无用管理']">
    <template #action>
      <BasicButtonImport
              perm-code="system:for-test:import"
              file-name="测试而已无用管理"
              :template-api="ForTestApi.importTemplate"
              :import-api="ForTestApi.importForTest"
              :exportError-file-api="ForTestApi.exportErrorFile"
              @success="handlerImportSuccess"
      />
      <BasicButtonExport
              perm-code="system:for-test:export"
              file-name="测试而已无用管理"
              :export-api="ForTestApi.exportForTest"
      />
      <el-button v-hasPermi="['system:for-test:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register"/>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { ForTestApi} from '@/api/system/fortest'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:for-test:update', callback: handleEdit },
    { label: '查看', permCode: 'system:for-test:query', callback: handleDetail },
    { label: '删除', permCode: 'system:for-test:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: ForTestApi.getForTestPage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: ForTestApi.getForTest,
    submitApi: id ? ForTestApi.updateForTest : ForTestApi.createForTest
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await ForTestApi.deleteForTest(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>