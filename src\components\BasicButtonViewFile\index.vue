<template>
  <el-button v-if="fileIds" link type="primary" style="font-weight: normal" @click="open">
    {{ name }}
  </el-button>
  <span v-else>--</span>
  <BasicDialog v-model:show="dialogVisible" :title="title" width="800px" :show-footer="fileInfoList.length > 0">
    <template #content>
      <BasicTable @register="register">
        <template #name="{ row }">
          <span class="text-blue-500" style="cursor: pointer" @click="handleFile(row)">
            {{ row.name }}
          </span>
        </template>
      </BasicTable>
    </template>
    <template #action>
      <el-button type="primary" :disabled="!fileIds" :loading="btnLoading" @click="handleDownload2">打包下载</el-button>
    </template>
  </BasicDialog>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import { useTable } from '@/components/BasicTable'
import { getFileInfoListByIds, downloadFileZip } from '@/api/infra/file'
import dayjs from 'dayjs'
// import { getAccessToken, getTenantId } from '@/utils/auth'
// import axios from 'axios'
// import JSZip from 'jszip'
// import FileSaver from 'file-saver'
import download from '@/utils/download'

defineOptions({ name: 'BasicButtonViewFile' })
const emit = defineEmits(['confirm', 'delete'])
const props = defineProps({
  name: propTypes.string.def('查看'),
  title: propTypes.string.def('查看附件'),
  fileIds: propTypes.string.def(''),
  zipName: propTypes.string.def('附件压缩包'),
  isShowAction: propTypes.bool.def(false)
})

// const message = useMessage()
const propFileIds = ref()
const dialogVisible = ref(false)
const tableColumns = [
  { label: '名称', slot: 'name', tooltip: true, minWidth: 180 },
  {
    label: '上传时间',
    prop: 'createTime',
    minWidth: 180,
    filter: (row: any) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
  },
  { label: '上传人员', minWidth: 140, prop: 'creatorNickname' }
]
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 100,
  buttons: [
    {
      label: '删除',
      permCode: '',
      color: 'red',
      callback: handleDelete
    }
  ]
}
const fileInfoList = ref([])
const btnLoading = ref(false)

const open = async () => {
  dialogVisible.value = true
  if (props.fileIds) {
    propFileIds.value = props.fileIds
    reload()
  }
}

const handleFile = (item: any) => {
  window.open(item.url)
}

// const getFile = (item: any) => {
//   return new Promise((resolve, reject) => {
//     axios({
//       method: 'get',
//       url: `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_URL}/infra/file/${item.configId}/get/${item.path}`,
//       responseType: 'arraybuffer'
//     })
//       .then((data: any) => {
//         resolve(data.data)
//       })
//       .catch((error: any) => {
//         console.log(error, 'error')
//         message.error(error.response.request.responseURL + ' 文件未找到！')
//         reject(error.toString())
//       })
//   })
// }

// 参数传给后端处理
const handleDownload2 = async () => {
  btnLoading.value = true
  const fileIds: any = []
  fileInfoList.value.forEach((item: any) => {
    fileIds.push(item.id)
  })
  const res = await downloadFileZip({
    fileIds,
    zipName: props.zipName
  })

  download.zip(res, `${props.zipName}.zip`)
  btnLoading.value = false
}

// 前端下载文件压缩处理
// const handleDownload = () => {
//   btnLoading.value = true
//   const zip = new JSZip()
//   const cache = {}
//   const promises: any = []
//   fileInfoList.value.forEach((item: any) => {
//     const promise = getFile(item).then((data: any) => {
//       // 下载文件, 并存成ArrayBuffer对象
//       const fileName = item.name
//       zip.file(fileName, data, { binary: true }) // 逐个添加文件
//       cache[fileName] = data
//     })
//     promises.push(promise)
//    })
//   Promise.all(promises).then(() => {
//     zip
//       .generateAsync({ type: 'blob' })
//       .then((content: any) => {
//         FileSaver.saveAs(content, `${props.zipName}.zip`) // 利用file-saver保存文件
//       })
//       .catch(() => {
//         console.log('文件压缩失败!')
//       })
//       .finally(() => {
//         btnLoading.value = false
//         // dialogVisible.value = false
//       })
//   })
// }

const [register, { reload }] = useTable({
  api: getFileInfoListByIds,
  columns: props.isShowAction ? [...tableColumns, actionsColumn] : tableColumns,
  pagination: false,
  // immediate: false,
  beforeFetch: () => {
    if (!props.fileIds) return false

    if (props.fileIds) {
      const ids = propFileIds.value.split(',')
      return { fileIds: ids }
    }
    return {}
  },
  afterFetch: (data: any) => {
    fileInfoList.value = data
  }
})

function handleDelete(row: any) {
  emit('delete', row)
  propFileIds.value = props.fileIds
    .split(',')
    .filter((ele) => ele != row.id)
    .join(',')
}
watch(
  () => props.fileIds,
  async (newValue) => {
    propFileIds.value = newValue
    reload()
  },
  { deep: true }
)

defineExpose({ open, reload })
</script>
