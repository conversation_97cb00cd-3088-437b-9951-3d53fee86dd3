import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '任务名称',
      prop: 'name'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '创建时间开始',
      endPlaceholder: '创建时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '流程', prop: 'processInstanceName', minWidth: 140, tooltip: true },
    { label: '发起人', prop: 'nickname', minWidth: 100, tooltip: true },
    { label: '发起时间', prop: 'createTime', minWidth: 180, tooltip: true },
    { label: '当前任务', prop: 'name', minWidth: 180, tooltip: true },
    { label: '任务开始时间', prop: 'createTime', minWidth: 180, tooltip: true },
    { label: '任务结束时间', prop: 'endTime', minWidth: 180, tooltip: true },
    { label: '审批人', prop: 'assigneeUserName', minWidth: 100, tooltip: true },
    {
      label: '审批状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.BPM_TASK_STATUS
    },
    { label: '审批建议', prop: 'reason', minWidth: 180, tooltip: true },
    { label: '耗时', prop: 'durationInMillis', minWidth: 100, tooltip: true },
    { label: '流程编号', prop: 'id', minWidth: 140, tooltip: true },
    { label: '任务编号', prop: 'id', minWidth: 140, tooltip: true }
  ]
})

