<template>
  <el-select-v2
    class="p-0 w-full"
    :value="value"
    clearable
    :options="innerOptions"
    v-bind="$attrs"
    @change="handleChange"
  >
    <template #default="{ item }">
      <span>{{ item.label }}</span>
    </template>
  </el-select-v2>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'

defineOptions({ name: 'BasicSelect' })

const emit = defineEmits(['update:value', 'update:label'])

const props = defineProps({
  value: propTypes.string.def(''),
  label: propTypes.string.def(''),
  options: propTypes.array.def([]),
  fieldNames: propTypes.object.def({})
})

const innerOptions: any = ref(props.options)

const handleChange = (val: string | Array<any>) => {
  if (Array.isArray(val)) {
    emit('update:value', val)
    return
  }
  // 查找并设置 selectedObject
  let selectedObject: any = {}
  if (val) {
    selectedObject = innerOptions.value.find((option: any) => option.value === val)
  } else {
    selectedObject = { value: '', label: '' }
  }
  emit('update:value', selectedObject.value)
  emit('update:label', selectedObject.label)
}

watch(
  () => props.options,
  () => {
    if (props.fieldNames.value || props.fieldNames.label) {
      innerOptions.value = props.options.map((item: any) => {
        const newItem = {}
        for (let key in props.fieldNames) {
          newItem[key] = item[props.fieldNames[key]]
        }
        return newItem
      })
      return
    }
    innerOptions.value = props.options
  },
  { immediate: true }
)
</script>
