<template>
  <div class="pmis-page-content">
    <el-row
      v-if="tabs.length === 1"
      class="header"
      :class="{ border: border }"
      justify="space-between"
      align="middle"
      :style="`padding:${titlePaddingS};fontSize:${titleFontSize}`"
    >
      <div class="title">{{ tabs[0] }}</div>
      <div><slot name="action"></slot></div>
    </el-row>
    <div v-else-if="tabs.length > 1">
      <el-row class="header hidden-sm-and-down" justify="space-between" align="middle">
        <!-- <ElButton v-if="back" text class="w-40px -ml-10px -mt-10px" @click="handleBack">
          <Icon icon="ep:back" size="22" />
        </ElButton> -->
        <div class="basic-tabs">
          <el-tabs v-model="tabActive">
            <el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab" :name="index" />
          </el-tabs>
          <div class="action">
            <slot name="action"></slot>
            <ElButton v-if="back" text class="w-40px" @click="handleBack">
              <!-- <Icon icon="ep:close" size="22" /> -->
              返回
            </ElButton>
          </div>
        </div>
      </el-row>
      <el-row class="header hidden-md-and-up" align="middle">
        <!-- <ElButton v-if="back" text class="w-40px -ml-10px -mt-10px" @click="handleBack">
          <Icon icon="ep:back" size="22" />
        </ElButton> -->
        <div class="basic-tabs">
          <el-tabs v-model="tabActive">
            <el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab" :name="index" />
          </el-tabs>
        </div>
        <div class="action mt-14px">
          <slot name="action"></slot>
          <ElButton v-if="back" text class="w-40px -ml-10px -mt-10px" @click="handleBack">
            <Icon icon="ep:close" size="22" />
          </ElButton>
        </div>
      </el-row>
    </div>
    <div><slot name="tip"></slot></div>
    <slot default></slot>
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import 'element-plus/theme-chalk/display.css'

defineOptions({ name: 'BasicPage' })

const emit = defineEmits(['update:tabIndex', 'back'])

const props = defineProps({
  back: propTypes.bool.def(false),
  tabIndex: propTypes.number.def(0),
  tabs: propTypes.array.def([]),
  border: propTypes.bool.def(true),
  titlePadding: propTypes.array.def([0, 0, 10]),
  showBottom: propTypes.bool.def(true),
  titleFontSize: propTypes.string.def('20px')
})

const router = useRouter()
const tabActive = ref(props.tabIndex)

const titlePaddingS = computed(() => {
  let padding = props.titlePadding
  padding.forEach((item: any, index: any) => {
    padding[index] = `${item}px`
  })
  return padding.join(' ')
})

const handleBack = () => {
  emit('back')
  router.back()
}

watch(
  () => tabActive.value,
  (val: number) => {
    emit('update:tabIndex', val)
  }
)
</script>

<style lang="scss" scoped>
.pmis-page-content {
  background-color: white;
  padding: 14px;
}
.title {
  color: #191919;
  // font-size: 20px;
  // font-weight: 500;
  font-weight: bold;
}
.header {
  margin-bottom: 14px;
}
.border {
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}
.basic-tabs {
  position: relative;
  // width: calc(100% - 40px);
  width: 100%;
  .action {
    position: absolute;
    top: 0;
    right: 0;
  }
  :deep(.el-tabs) {
    .el-tabs__header {
      margin-bottom: 0;
    }
    .el-tabs__item {
      align-items: flex-start;
      padding-top: 4px;
      font-size: 16px;
    }
    .el-tabs__nav {
      height: 44px;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      margin-top: -5px;
    }
  }
}
</style>
