import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '流程名称',
      prop: 'name'
    },
    {
      component: 'input',
      label: '所属流程',
      prop: 'processDefinitionId'
    },
    {
      label: '流程分类',
      slot: 'category'
    },
    {
      component: 'select',
      label: '流程状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '发起时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '发起时间开始',
      endPlaceholder: '发起时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '流程名称', prop: 'name', minWidth: 100, tooltip: true },
    {
      label: '流程状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS
    },
    { label: '审批编号', minWidth: 180, prop: 'approvalNo' },
    { label: '发起时间', minWidth: 180, prop: 'startTime' },
    { label: '结束时间', minWidth: 180, prop: 'endTime' },
    { label: '耗时', minWidth: 180, prop: 'durationInMillis' },
    { label: '当前审批任务', minWidth: 180, slot: 'tasks' }
  ]
})
