<template>
  <el-button v-if="hasPermi" :size="size" @click="visible = true">
    <span>{{ title }}</span>
  </el-button>
  <Dialog v-if="visible" v-model="visible" :title="headerTitle" width="800px" append-to-body
    :close-on-click-modal="false" :close-on-press-escape="false">
    <el-row align="middle" class="mb-10px">
      模板示例文件:
      <el-button class="!pl-10px" :loading="btnTemplateLoading" type="text" @click="handleDownloadTemplate">
        {{ templateName }}
      </el-button>
    </el-row>
    <div class="upload-container">
      <el-upload ref="uploadRef" v-model:file-list="defaultFileList" :auto-upload="false" :disabled="btnOkLoading"
        :limit="1" :on-exceed="handleExceed" accept=".xlsx, .xls" drag>
        <Icon icon="ep:upload" />
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
      </el-upload>
    </div>
    <div>{{ hint }}</div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button :loading="btnOkLoading" type="primary" @click="handleOk">确定导入</el-button>
    </template>
  </Dialog>

  <Dialog v-if="previewVisible" v-model="previewVisible" :title="errorFileName" width="1000px"
    :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
    <div class="scroll">
      <vue-office-excel :src="excelData" class="excel" @rendered="rendered" />
    </div>
    <template #footer>
      <el-button @click="previewVisible = false">取消</el-button>
      <el-button :loading="btnDownloadLoading" type="primary" @click="handleDownloadErrorData">下载</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import download from '@/utils/download'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

defineOptions({ name: 'BasicButtonExport' })

const message = useMessage()
const { wsCache } = useCache()
const emit = defineEmits(['success', 'fail'])

const props = defineProps({
  title: propTypes.string.def('导入'),
  permCode: propTypes.string.def(''),
  fileName: propTypes.string.def(''),
  params: propTypes.object.def({}), // 额外参数
  templateApi: {
    type: Function
  },
  importApi: {
    type: Function
  },
  exportErrorFileApi: {
    type: Function
  },
  hint: propTypes.string.def('支持xls、xlsx格式文件，为保障上传成功，建议一次不要超过1万条数据'),
  size: propTypes.string.def('default')
})

const hasPermi = ref(true)

const visible = ref(false)
const btnTemplateLoading = ref(false)
const btnOkLoading = ref(false)
const defaultFileList: any = ref(undefined)

const previewVisible = ref(false)
const errorTableData: any = ref([])
const btnDownloadLoading = ref(false)

const excelData = ref('')
const errorExcelData: any = ref()

const headerTitle = computed(() => `${props.fileName} 导入`)

const templateName = computed(() => {
  return `${props.fileName}导入模板`
})

const errorFileName = computed(() => {
  return `${props.fileName}导入包含的错误数据`
})

const handleDownloadTemplate = async () => {
  if (!props.templateApi) return
  try {
    btnTemplateLoading.value = true
    const res = await props.templateApi()
    download.excel(res, `${templateName.value}.xlsx`)
  } finally {
    btnTemplateLoading.value = false
  }
}

// 文件数超出提示
const handleExceed = () => {
  message.error('上传文件数量不能超过1个!')
}

const handlePreview = async (data: any) => {
  if (!props.exportErrorFileApi) return
  try {
    previewVisible.value = true
    btnDownloadLoading.value = true
    errorExcelData.value = await props.exportErrorFileApi(data)
    const res2 = await errorExcelData.value.arrayBuffer()
    excelData.value = res2
  } finally {
    btnDownloadLoading.value = false
  }
}

const handleDownloadErrorData = async () => {
  download.excel(errorExcelData.value, `${errorFileName.value}.xlsx`)
}

const handleOk = async () => {
  if (!props.importApi) {
    // emit('success')
    return
  }

  try {
    if (defaultFileList.value.length === 0) {
      message.warning('请选择文件导入！')
      return
    }
    btnOkLoading.value = true
    const { raw: file } = defaultFileList.value[0]
    const formData = new FormData()
    formData.append('file', file)
    const res = await props.importApi({ file, ...props.params })
    const { code, data = [], message: msg } = res
    visible.value = false
    if (code !== 0) {
      message.success(msg)
      return
    }
    if (data?.errorList?.length > 0) {
      errorTableData.value = data.errorList
      handlePreview(errorTableData.value)
      return
    }
    message.success('导入成功')
    emit('success')
  } finally {
    btnOkLoading.value = false
  }
}

const rendered = () => {
  console.log('渲染完成')
}

watch(
  () => visible.value,
  (val) => {
    if (!val) {
      defaultFileList.value = []
    }
  }
)

onMounted(() => {
  const permissions = wsCache.get(CACHE_KEY.USER).permissions
  const hasPermissions = permissions.some((permission: string) => permission.includes(props.permCode) || !props.permCode)
  hasPermi.value = hasPermissions
})
</script>

<style scoped>
.scroll {
  overflow-y: auto;
  height: 500px;

  .excel {
    height: 500px;
  }
}
</style>
