import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
  import {DocumentsApi} from "@/api/system/smc/documents";
  import {MedicineApi} from "@/api/system/smc/medicine";
  import {DeptApi} from "@/api/system/dept/deptOption";
  import {UsersApi} from "@/api/system/user/userOption";

export const formSearchConfig = {
  itemList: [
    {
      component: 'datePickerRange',
      label: '取药（分药）日期',
      prop: 'acquireDate',
      startPlaceholder: '取药（分药）日期开始日期',
      endPlaceholder: '取药（分药）日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'input',
      label: '操作记录单号',
      prop: 'recordNumber',
      placeholder: '请输入操作记录单号'
    },
    {
      component: 'select',
      label: '单据ID',
      prop: 'documentId',
      placeholder: '请选择单据ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: DocumentsApi.getDocumentsList,
    },
    {
      component: 'input',
      label: '关联单据编号',
      prop: 'documentNo',
      placeholder: '请输入关联单据编号'
    },
    {
      component: 'select',
      label: '药品ID',
      prop: 'medicineId',
      placeholder: '请选择药品ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: MedicineApi.getMedicineList,
    },
    {
      component: 'input',
      label: '国家药品标识码（唯一）',
      prop: 'nationalDrugCode',
      placeholder: '请输入国家药品标识码（唯一）'
    },
    {
      component: 'treeSelect',
      label: '部门ID',
      prop: 'deptId',
      placeholder: '请选择部门ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: DeptApi.getDeptList,
    },
    {
      component: 'select',
      label: '医生ID',
      prop: 'doctorId',
      placeholder: '请选择医生ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: UsersApi.getUsersList,
    },
    {
      component: 'datePickerRange',
      label: '医生确认时间',
      prop: 'doctorConfirmTime',
      startPlaceholder: '医生确认时间开始日期',
      endPlaceholder: '医生确认时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'input',
      label: '批次号',
      prop: 'batchNumber',
      placeholder: '请输入批次号'
    },
    {
      component: 'input',
      label: '药品追溯码',
      prop: 'traceCode',
      placeholder: '请输入药品追溯码'
    },
    {
      component: 'number',
      label: '数量',
      prop: 'quantity',
      placeholder: '请输入数量'
    },
    {
      component: 'datePickerRange',
      label: '生产日期',
      prop: 'productionDate',
      startPlaceholder: '生产日期开始日期',
      endPlaceholder: '生产日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'datePickerRange',
      label: '有效期截止日期',
      prop: 'expiryDate',
      startPlaceholder: '有效期截止日期开始日期',
      endPlaceholder: '有效期截止日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '取药（分药）日期',
      prop: 'acquireDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作记录单号',
      prop: 'recordNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单据ID',
      prop: 'documentIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '关联单据编号',
      prop: 'documentNo',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品ID',
      prop: 'medicineIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '国家药品标识码（唯一）',
      prop: 'nationalDrugCode',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '部门ID',
      prop: 'deptIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '医生ID',
      prop: 'doctorIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '医生确认时间',
      prop: 'doctorConfirmTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '批次号',
      prop: 'batchNumber',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品追溯码',
      prop: 'traceCode',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '数量',
      prop: 'quantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '生产日期',
      prop: 'productionDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '有效期截止日期',
      prop: 'expiryDate',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '分药医生药品关联',
    items: [
      {
        component: 'datePicker',
        label: '取药（分药）日期',
        prop: 'acquireDate',
        placeholder: '选择取药（分药）日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'input',
        label: '操作记录单号',
        prop: 'recordNumber',
        placeholder: '请输入操作记录单号',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '单据ID',
        prop: 'documentId',
        placeholder: '请选择单据ID',
        params: {},
        filterable: true,
        propName: 'documentIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: DocumentsApi.getDocumentsList,
      },
      {
        component: 'input',
        label: '关联单据编号',
        prop: 'documentNo',
        placeholder: '请输入关联单据编号',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '药品ID',
        prop: 'medicineId',
        placeholder: '请选择药品ID',
        params: {},
        filterable: true,
        propName: 'medicineIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: MedicineApi.getMedicineList,
      },
      {
        component: 'input',
        label: '国家药品标识码（唯一）',
        prop: 'nationalDrugCode',
        placeholder: '请输入国家药品标识码（唯一）',
        rules: [
              { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'treeSelect',
        label: '部门ID',
        prop: 'deptId',
        placeholder: '请选择部门ID',
        filterable: true,
        params: {},
        propName: 'deptIdName',
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        api: DeptApi.getDeptList,
        rules: [{ required: true, message: '部门ID不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '医生ID',
        prop: 'doctorId',
        placeholder: '请选择医生ID',
        params: {},
        filterable: true,
        propName: 'doctorIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: UsersApi.getUsersList,
        rules: [{ required: true, message: '医生ID不能为空', trigger: 'change' }]
      },
      {
        component: 'datePicker',
        label: '医生确认时间',
        prop: 'doctorConfirmTime',
        placeholder: '选择医生确认时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'input',
        label: '批次号',
        prop: 'batchNumber',
        placeholder: '请输入批次号',
        rules: [
              { required: true, message: '批次号不能为空', trigger: 'blur' },
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '药品追溯码',
        prop: 'traceCode',
        placeholder: '请输入药品追溯码',
        rules: [
              { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '数量',
        prop: 'quantity',
        max: 9999999999,
        placeholder: '请输入数量',
      },
      {
        component: 'datePicker',
        label: '生产日期',
        prop: 'productionDate',
        placeholder: '选择生产日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'datePicker',
        label: '有效期截止日期',
        prop: 'expiryDate',
        placeholder: '选择有效期截止日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'number',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        max: 999,
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
    ]
  }
])
