<template>
  <BasicPage :tabs="['已办任务']">
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { formatPast2 } from '@/utils/formatTime'
import * as TaskApi from '@/api/bpm/task'

defineOptions({ name: 'BpmTodoTask' })

const router = useRouter()

const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 100,
  buttons: [{ label: '历史', permCode: '', callback: handleDetail }]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: TaskApi.getTaskDonePage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  },
  afterFetch: (res) => {
    res.forEach((item: any) => {
      item.processInstanceName = item.processInstance.name
      item.nickname = item.processInstance.startUser.nickname
      item.durationInMillis = formatPast2(item.durationInMillis)
    })
  }
})

const searchFn = () => {
  reload()
}

/** 查看历史 */
function handleDetail(row) {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}
</script>

<style></style>
