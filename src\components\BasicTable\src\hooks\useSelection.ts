// import { ref, computed } from 'vue'

import { isFunction } from '@/utils/is'
import { ComputedRef, unref } from 'vue'

/**
 * @description 表格多选数据操作
 * @param propsRef
 * */
export const useSelection = (propsRef: ComputedRef) => {
  const isSelected = ref<boolean>(false)
  const selectedList = ref<{ [key: string]: any }[]>([])
  const currentRow = ref<any>(null)

  // 当前选中的所有 ids 数组
  const selectedListIds = computed((): string[] => {
    const ids: string[] = []
    selectedList.value.forEach((item) => ids.push(item[unref(propsRef).rowKey ?? 'id']))
    return ids
  })

  /**
   * @description 多选操作
   * @param {Array} rowArr 当前选择的所有数据
   * @return void
   */
  const selectionChange = (rowArr: { [key: string]: any }[]) => {
    rowArr.length ? (isSelected.value = true) : (isSelected.value = false)
    selectedList.value = rowArr
    if (unref(propsRef).selectionChange && isFunction(unref(propsRef).selectionChange)) {
      unref(propsRef).selectionChange(
        unref(isSelected),
        unref(selectedList),
        unref(selectedListIds)
      )
    }
  }

  const currentChange = (row: { [key: string]: any }) => {
    currentRow.value = row
    if (unref(propsRef).currentChange && isFunction(unref(propsRef).currentChange)) {
      unref(propsRef).currentChange(row)
    }
  }

  return {
    isSelected,
    selectedList,
    selectedListIds,
    selectionChange,
    currentChange,
    currentRow
  }
}
