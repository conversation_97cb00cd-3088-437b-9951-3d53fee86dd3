<template>
  <BasicPage :tabs="['待办任务']">
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as TaskApi from '@/api/bpm/task'
import { ProjectBaseInfoApi } from '@/api/pmis/project/basic'

defineOptions({ name: 'BpmTodoTask' })

const { push } = useRouter() // 路由

const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 100,
  buttons: [{ label: '办理', permCode: '', callback: handleAudit }]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: TaskApi.getTaskTodoPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  },
  afterFetch: async (data) => {
    const requestData = {}
    data.forEach((item: any) => {
      if (item.businessKey && item.flowCode) {
        const processKey = `${item.businessKey}_${item.flowCode}`
        item.processKey = processKey
        requestData[processKey] = item.flowCode
      }
    })
    const processFormInfoRes = await ProjectBaseInfoApi.getProcessFormInfo({ params: requestData })
    data.forEach((item: any) => {
      if (item.businessKey && item.flowCode) {
        // item.formInfo = processFormInfoRes[item.processKey]
        const formInfo = processFormInfoRes[item.processKey]
        item.approvalNo = formInfo?.processFormInfo?.approvalNo || '--'
      }
      item.processInstanceName = item.processInstance.name
      item.nickname = item.processInstance.startUser.nickname
    })
  }
})

/** 处理审批按钮 */
function handleAudit(row: any) {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}

const searchFn = () => {
  reload()
}
</script>

<style></style>
