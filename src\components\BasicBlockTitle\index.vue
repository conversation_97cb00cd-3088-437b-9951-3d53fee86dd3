<template>
  <div class="block-title">
    <div class="line"></div>
    <div :style="`font-size: ${size}px;`">{{ title }}</div>
  </div>
</template>

<script lang="ts" setup name="BasicBlockTitle">
import { propTypes } from '@/utils/propTypes'

defineProps({
  title: propTypes.string.def('标题'),
  size: propTypes.number.def(14)
})
</script>

<style lang="scss" scoped>
.block-title {
  display: flex;
  align-items: center;
  // height: 40px;
  // color: var(--main-text-color);
  font-weight: 500;
  // font-size: 14px;
  .line {
    width: 4px;
    height: 15px;
    border-radius: 2px;
    background-color: var(--el-color-primary);
    margin-right: 6px;
  }
}
</style>
