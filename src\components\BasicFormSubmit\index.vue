<template>
  <el-form ref="baseForm" :model="innerFormData" class="base-form" :label-position="labelPosition" :label-width="preLabelWidth">
    <div v-for="(category, cIdx) in innerFormObjList" :key="cIdx">
      <BasicBlockTitle
        v-if="category.title"
        :title="category.title"
        :style="{
          marginTop: cIdx === 0 ? '0' : '10px',
          marginBottom: '10px'
        }"
      />
      <div class="form-container">
        <el-form-item
          v-for="(formObj, index) in category.items"
          v-show="!formObj.hidden"
          :key="formObj.prop"
          :label="`${formObj.label} ${isColonFn(formObj)}`"
          :prop="formObj.prop"
          :rules="isView ? [] : formObj.rules"
          :required="formObj.required"
          :style="`${formObj.singleLine ? 'width: 100%' : 'width: calc(50% - 10px)'}`"
        >
          <!-- 预览模式 -->
          <template v-if="isView && formObj.component !== 'upload-file' && formObj.component !== 'image'">
            <div v-if="formObj.slot" class="view-value">
              <!-- {{ innerFormData[formObj.slot] || '--' }} -->
              <template v-if="formObj.slot">
                <!-- 父子两级 -->
                <slot :name="formObj.slot"></slot>
                <!-- 多层极 -->
                <slot name="sub-slot" :prop-name="formObj.slot"></slot>
              </template>
            </div>
            <div v-else class="view-value">
              <div
                v-if="(formObj.component === 'select' || formObj.component === 'treeSelect') && innerFormData[formObj.prop] !== null && innerFormData[formObj.prop] !== undefined"
              >
                  <div v-if="formObj.api || !formObj.options">
                    {{ innerFormData[formObj.propName]?innerFormData[formObj.propName]:'--'}}
                  </div>
                  <div v-else>
                    <template v-for="opt in formObj.options">
                      {{ innerFormData[formObj.prop] === opt.value ? opt.label : '' }}
                    </template>
                  </div>
              </div>
              <div v-else-if="formObj.component === 'road-select'">
                {{ innerFormData[formObj.prop]?.label }}
              </div>
              <div v-else-if="formObj.component === 'editor'">
                <div v-html="innerFormData[formObj.prop]"></div>
              </div>
              <div v-else-if="formObj.component === 'img'">
                <img :src="innerFormData[formObj.prop]" class="upload-image" />
              </div>
              <div v-else>
                
                {{ innerFormData[formObj.prop] === null || innerFormData[formObj.prop] === undefined|| !innerFormData[formObj.prop]? '--' : innerFormData[formObj.prop] }}
              </div>
            </div>
          </template>
          <!-- 表单模式 -->
          <template v-else>
            <!-- 自定义 -->
            <div class="form-slot">
              <template v-if="formObj.slot">
                <!-- 父子两级 -->
                <slot :name="formObj.slot"></slot>
                <!-- 多层极 -->
                <slot name="sub-slot" :prop-name="formObj.slot"></slot>
              </template>
            </div>
            <!-- inupt 输入框 -->
            <el-input
              v-if="formObj.component === 'input'"
              v-model="innerFormData[formObj.prop]"
              :type="formObj.type"
              :readonly="formObj.readonly"
              :disabled="formObj.disabled"
              :placeholder="handlePlaceholder(formObj)"
              :maxlength="formObj.maxlength || 50"
              :show-password="formObj.showPassword"
              auto-complete="new-password"
              :clearable="!formObj.readonly || formObj.clearable"
              :style="`${formObj.unit ? 'width: calc(100% - 40px)' : 'width: 100%'}`"
              @input="inputINPUT($event, index, formObj.prop)"
              @change="formObj.change ? formObj.change($event, index, formObj.prop) : null"
            />
          <!--  数字       -->
            <el-input-number
              v-if="formObj.component === 'number'"
              v-model="innerFormData[formObj.prop]"
              :readonly="formObj.readonly"
              :disabled="formObj.disabled"
              :placeholder="handlePlaceholder(formObj)"
              :min="formObj.min || 0"
              :max="formObj.max"
              :style="`${formObj.unit ? 'width: calc(100% - 40px)' : 'width: 100%'}`"
              :clearable="!formObj.readonly || formObj.clearable"
              controls-position="right"
            />
            <!-- textarea 输入框 -->
            <el-input
              v-if="formObj.component === 'textarea'"
              v-model="innerFormData[formObj.prop]"
              type="textarea"
              :rows="formObj.rows || 4"
              :readonly="formObj.readonly"
              :disabled="formObj.disabled"
              :show-word-limit="formObj.showWordLimit"
              :maxlength="formObj.maxlength || 2000"
              clearable
              :placeholder="handlePlaceholder(formObj)"
            />
            <!-- radio 单选框 -->
            <el-radio-group
              v-if="formObj.component === 'radio'"
              v-model="innerFormData[formObj.prop]"
              :disabled="formObj.disabled"
              :readonly="formObj.readonly"
              @change="formObj.change ? formObj.change($event, index, formObj.prop, handleRelevance) : null"
            >
              <el-radio v-for="ops in formObj.options" :key="ops.value" :label="ops.value">
                {{ ops.label }}
              </el-radio>
            </el-radio-group>
            <!-- switch 开关 -->
            <el-switch
              v-if="formObj.component === 'switch'"
              v-model="innerFormData[formObj.prop]"
              :disabled="formObj.disabled"
              :active-value="formObj.customValue ? formObj.activeValue : true"
              :inactive-value="formObj.customValue ? formObj.inactiveValue : false"
              @change="formObj.change ? formObj.change($event, index, formObj.prop, handleRelevance) : null"
            />
            <!-- checkbox 复选框 -->
            <el-checkbox-group
              v-if="formObj.component === 'checkbox'"
              v-model="innerFormData[formObj.prop]"
              :disabled="formObj.disabled"
              @change="formObj.change ? formObj.change($event, index, formObj.prop, handleRelevance) : null"
            >
              <!-- 返回的value数组 -->
              <div v-if="formObj.returnValue">
                <el-checkbox v-for="ops in formObj.options" :key="ops.value" :label="ops.value">
                  {{ ops.label }}
                </el-checkbox>
              </div>
              <!-- 返回的label数组 -->
              <div v-else>
                <el-checkbox v-for="ops in formObj.options" :key="ops.value" :label="ops.value" :value="ops.value" :data-value="ops.value">
                  {{ ops.label }}
                </el-checkbox>
              </div>
            </el-checkbox-group>

            <!-- select  -->
            <el-select-v2
              v-else-if="formObj.component === 'select'"
              v-model="innerFormData[formObj.prop]"
              :filterable="formObj.filterable !== undefined ? formObj.filterable : true"
              :options="formObj.options"
              :disabled="formObj.disabled"
              :remote="formObj.remote"
              :remote-method="(query)=>{selectRemoteMethod(query, formObj)}"
              :loading="selectLoading"
              :placeholder="handlePlaceholder(formObj)"
              :multiple="formObj.multiple"
              :style="`${formObj.unit ? 'width: calc(100% - 40px)' : 'width: 100%'}`"
              clearable
              @change="formObj.change ? formObj.change($event, index, formObj.prop, handleRelevance) : null"
            />
            <!-- treeSelect  -->
            <el-tree-select
              v-else-if="formObj.component === 'treeSelect'"
              v-model="innerFormData[formObj.prop]"
              :filterable="formObj.filterable !== undefined ? formObj.filterable : true"
              :data="formObj.options"
              :disabled="formObj.disabled"
              :placeholder="handlePlaceholder(formObj)"
              :multiple="formObj.multiple"
              :style="`${formObj.unit ? 'width: calc(100% - 40px)' : 'width: 100%'}`"
              clearable
              check-strictly
              @change="formObj.change ? formObj.change($event, index, formObj.prop, handleRelevance) : null"
            />

            <el-cascader 
              v-else-if="formObj.component === 'cascader'"
              v-model="innerFormData[formObj.prop]"
              :filterable="formObj.filterable !== undefined ? formObj.filterable : true"
              :disabled="formObj.disabled"
              :options="formObj.options" 
              :props="formObj.props"
              :placeholder="handlePlaceholder(formObj)"
              :multiple="formObj.multiple"
              :style="`${formObj.unit ? 'width: calc(100% - 40px)' : 'width: 100%'}`"
              clearable
              @change="formObj.change ? formObj.change($event, index, formObj.prop, handleRelevance) : null"
            />

            <!-- 年月日选择器 -->
            <el-date-picker
              v-if="formObj.component === 'datePicker'"
              v-model="innerFormData[formObj.prop]"
              :type="formObj.type"
              :value-format="formObj.dateFormate || 'YYYY-MM-DD'"
              :disabled="formObj.disabled"
              :readonly="formObj.readonly"
              :placeholder="handlePlaceholder(formObj)"
              style="width: 100%"
            />
            <!-- 年月日选择器，开始和结束年月日 -->
            <el-date-picker
              v-else-if="formObj.component === 'datePickerRange'"
              v-model="innerFormData[formObj.prop]"
              :value-format="formObj.dateFormate"
              type="daterange"
              :disabled="formObj.disabled"
              :readonly="formObj.readonly"
              :placeholder="handlePlaceholder(formObj)"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
            <!-- 时分秒选择器 -->
            <el-time-picker
              v-if="formObj.component === 'timePicker'"
              v-model="innerFormData[formObj.prop]"
              value-format="HH:mm:ss"
              style="width: 100%"
              format="HH:mm:ss"
              :disabled="formObj.disabled"
              :readonly="formObj.readonly"
              :placeholder="handlePlaceholder(formObj)"
              :picker-options="formObj.options"
            />
            <!-- 时分秒选择器，开始和结束时间 -->
            <el-time-picker
              v-if="formObj.component === 'timePickerRange'"
              v-model="innerFormData[formObj.prop]"
              :format="formObj.format"
              :value-format="formObj.dateFormate"
              is-range
              :disabled="formObj.disabled"
              :readonly="formObj.readonly"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @blur="moutedFn"
            />
            <!-- editor，开始和结束时间 -->
            <Editor
              v-if="formObj.component === 'editor'"
              v-model="innerFormData[formObj.prop]"
              :readonly="formObj.readonly"
              :disabled="formObj.disabled"
              :show-word-limit="formObj.showWordLimit"
              :maxlength="formObj.maxlength || 2000"
              clearable
              :placeholder="handlePlaceholder(formObj)"
              height="150px"
            />

            <!-- 计量单位 -->
            <span v-if="formObj.unit" style="text-align: right; min-width: 40px">
              {{ formObj.unit }}
            </span>


          </template>
          <!-- @success="handleUploadFileSuccess(formObj.prop)" -->
          <BasicUploadFile
            v-if="formObj.component === 'upload-file'"
            v-model:fileIds="innerFormData[formObj.prop]"
            ref="basicUploadFileRef"
            :is-view="isView"
            :all-permission="formObj.allPermission"
            :limit="formObj.limit?formObj.limit:1"
            :file-type="formObj.fileType"
            :list-type="formObj.listType"
            :file-size="formObj.fileSize"
            :file-width="formObj.fileWidth"
            :is-compress="formObj.isCompress"
            :is-copper="formObj.isCopper"
            :tip="formObj.tip"
            style="width: 100%"
          />

          <BasicImageView v-if="formObj.component === 'image'" :width="80" :height="80" :file-ids="innerFormData[formObj.prop]" />
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script lang="ts" setup name="BasicFormSubmit">
import { propTypes } from '@/utils/propTypes'
import { ElMessage } from 'element-plus'
import { handleTree } from "@/utils/tree";

const emit = defineEmits(['inputINPUT'])

const props = defineProps({
  isView: propTypes.bool.def(true), // component是否为查看状态
  labelPosition: propTypes.string.def('right'),
  labelWidth: propTypes.string.def('110px'),
  objList: propTypes.array.isRequired,
  data: propTypes.object.isRequired
})

const handlePlaceholder = (formObj: any) => {
  const { label, placeholder, component } = formObj
  if (['input', 'number', 'textarea'].includes(component)) {
    return placeholder || `请输入${label}`
  } else {
    return placeholder || `请输入${label}`
  }
}

const moutedFn = () => {
  innerFormObjList._value[7].component = ''
}

const preLabelWidth = computed(() => (props.labelPosition === 'top' ? '' : props.labelWidth))
let innerFormObjList: any = ref([])
let innerFormData: any = ref({})
const selectLoading = ref(false)

const isColonFn = (item: { label: String; isColon: Boolean }) => {
  return !item.label || item.isColon ? '' : ':'
}

// input的input事件
const inputINPUT = (e: Event, index: Number, prop: any) => {
  emit('inputINPUT', e, index, prop)
}

// 提交时验证表单，直接在父级调用
const baseForm = ref()
const submitForm = (checkPrompt = true) => {
  return baseForm.value.validate((valid: any) => {
    if (valid) return true

    if (checkPrompt) {
      ElMessage({ message: '信息不完整，请检查必填项！', type: 'warning' })
    }
    return false
  })
}

/** 清空表单 */
const resetForm = () => {
  baseForm.value.resetFields()
}

const basicUploadFileRef = ref()
const uploadFileFinishList = () => {
  return basicUploadFileRef.value
}

// select自定义远程搜索方法
const selectRemoteMethod = async (query: string, config: any) => {
  if (query !== '') {
    if(config.api){
      selectLoading.value = true
      let queryKey = config.queryKey || 'keyword'
      let queryParams = { ...config.params, ...{ [queryKey]: query } }
      const res = await config.api(queryParams)
      const tempList = res.map((option: any) => {
        const newItem = {}
        for (let key in config.fieldNames) {
          newItem[key] = option[config.fieldNames[key]]
        }
        return newItem
      })
      config.options = tempList
      selectLoading.value = false
    }
  }
}

// 处理表单数据关联性问题
const handleRelevance = (prop:string, fun:Function, params: any,) => {
  innerFormObjList.value.forEach((item: any) => {
    item.items.forEach(async (formObj:any) => {
      if (formObj.prop === prop) {
        if(innerFormData.value[prop]) innerFormData.value[prop] = ''
        if (!fun) return
        const res = await fun(params)
        const tempList = res.map((option: any) => {
          const newItem = {}
          for (let key in formObj.fieldNames) {
            newItem[key] = option[formObj.fieldNames[key]]
          }
          return newItem
        })
        if(formObj.component == 'select'){
          formObj.options = tempList
        }
        if(formObj.component == 'treeSelect'){
          formObj.options = handleTree(tempList);
        }
      }
    })
  })
}

// const handleUploadFileSuccess = (prop: string) => {
//   baseForm.value.validateField(prop, (valid: boolean) => {
//     if (!valid) return false
//   })
// }

watchEffect(
  () => {
    innerFormObjList.value = props.objList
    innerFormData.value = props.data
  },
  { flush: 'sync' }
)

// watch(
//   () => innerFormData.value,
//   () => baseForm.value.validate(),
//   { deep: true }
// )

defineExpose({ submitForm, resetForm, uploadFileFinishList, handleRelevance })
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  .el-form-item__label:before {
    content: '';
    width: 6px;
    height: 100%;
    margin-right: 4px;
  }
}
.base-form {
  .form-container {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    .form-slot {
      width: 100%;
    }
    .view-value {
      width: 100%;
      font-weight: 500;
      background-color: rgb(242, 243, 245);
      margin: 0 5px;
      padding: 0 8px;
    }
    .el-form-item {
      margin-bottom: 20px;
      .el-radio-group {
        display: flex;
        align-items: center;
        height: 32px;
        .el-radio {
          margin-left: 0;
          margin-right: 20px;
          height: 30px;
        }
      }
      .el-checkbox-group {
        display: flex;
        align-items: center;
        .el-checkbox {
          margin-left: 0;
          margin-right: 20px;
        }
      }
      .el-select-v2 {
        text-align: left;
      }
      // .el-select-v2,
      .el-textarea,
      .el-radio-group {
        width: 100%;
        height: 100%;
      }
    }
    // ::v-deep(.el-radio__label) {
    //   padding-left: 5px !important;
    // }
    // ::v-deep(.el-checkbox__label) {
    //   padding-left: 5px !important;
    // }
    ::v-deep(.el-input__inner) {
      text-align: left;
    }
  }
}
</style>
