import request from '@/config/axios'

// 盘点明细 VO
export interface InventoryCheckDetailVO {
}

// 盘点明细 API
export const InventoryCheckDetailApi = {
  // 查询盘点明细分页
  getInventoryCheckDetailPage: async (params: any) => {
    return await request.get({ url: `/system/inventory-check-detail/page`, params })
  },
  // 查询盘点明细列表
  getInventoryCheckDetailList: async (params) => {
    return await request.get({ url: `/system/inventory-check-detail/list`, params })
  },

  // 查询盘点明细详情
  getInventoryCheckDetail: async (params: any) => {
    return await request.get({ url: `/system/inventory-check-detail/get`, params })
  },

  // 新增盘点明细
  createInventoryCheckDetail: async (data: InventoryCheckDetailVO) => {
    return await request.post({ url: `/system/inventory-check-detail/create`, data })
  },

  // 修改盘点明细
  updateInventoryCheckDetail: async (data: InventoryCheckDetailVO) => {
    return await request.put({ url: `/system/inventory-check-detail/update`, data })
  },

  // 删除盘点明细
  deleteInventoryCheckDetail: async (id: number) => {
    return await request.delete({ url: `/system/inventory-check-detail/delete?id=` + id })
  },

  // 导出盘点明细 Excel
  exportInventoryCheckDetail: async (params) => {
    return await request.download({ url: `/system/inventory-check-detail/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/inventory-check-detail/get-import-template` })
  },

  // 导入盘点明细 Excel
  importInventoryCheckDetail: async (formData) => {
    return await request.upload({ url: `/system/inventory-check-detail/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/inventory-check-detail/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}