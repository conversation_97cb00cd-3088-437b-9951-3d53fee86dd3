<template>
  <template v-if="computeOverflow(value, width)">
    <el-tooltip placement="top">
      <template #content>
        <div class="tooltip-box">{{ value }}</div>
      </template>
      <span :class="[`tooltip-text-${overflowRow}`]">{{ value }}</span>
    </el-tooltip>
  </template>
  <span v-else>{{ value }}</span>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  value: propTypes.string.def(''),
  width: propTypes.number.def(0),
  overflowRow: propTypes.number.def(1)
})

/**
 * @description: 获取 实际宽度、每行至多字符、最多几行
 * @param {*}
 * @return {*}
 */
const getOverFlowData = (width) => {
  // 实际装文字的宽度，table cell有总共16的内边距  通过查看元素 发现还有 1px 需要减去
  const realWidth = width - 16
  // 一行至多存在字符数 一个字符 7px  （文字font-size为14px）
  const rowChar = Math.floor(realWidth / 7)
  // 获取 至多 显示 几行
  const row = props.overflowRow

  return { rowChar, row }
}
/**
 * @description: 是否显示tooltip
 * @param {*}
 * @return {*}
 */
const computeOverflow = (val, width) => {
  if (typeof val !== 'string' || !width) return false
  // 内容字符长度
  let len = 0
  for (let i = 0; i < val.length; i++) {
    // const code = val.charCodeAt(i);
    // len++;
    // // 中文算两个字符;
    // if (code > 255) {
    //   len++;
    // }
    len++
    const regexAlpha = /[a-zA-Z]/ // 包含字母
    const regexChinese = /[\u4e00-\u9fa5]/ // 包含中文字符
    if (regexAlpha.test(val[i]) || regexChinese.test(val[i])) {
      len++
    }
  }
  const { rowChar, row } = getOverFlowData(width)
  return rowChar * row < len
}
</script>

<style lang="scss" scoped>
.tooltip-box {
  max-width: 300px;
  white-space: pre-wrap;
}
.tooltip-text-1,
.tooltip-text-2,
.tooltip-text-3,
.tooltip-text-4 {
  display: inline-block;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.tooltip-text-1 {
  -webkit-line-clamp: 1;
}

.tooltip-text-2 {
  -webkit-line-clamp: 2;
}
.tooltip-text-3 {
  -webkit-line-clamp: 3;
}
.tooltip-text-4 {
  -webkit-line-clamp: 4;
}
</style>
