<template>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <DeptTree @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <BasicPage :tabs="['基数管理']">
        <template #action>
          <BasicButtonImport perm-code="system:medicine-dept-config:import" file-name="基数管理"
            :template-api="MedicineDeptConfigApi.importTemplate"
            :import-api="MedicineDeptConfigApi.importMedicineDeptConfig"
            :exportError-file-api="MedicineDeptConfigApi.exportErrorFile" @success="handlerImportSuccess" />
          <BasicButtonExport perm-code="system:medicine-dept-config:export" file-name="基数管理" :params="{ ...searchForm }"
            :export-api="MedicineDeptConfigApi.exportMedicineDeptConfig" />
          <el-button v-hasPermi="['system:medicine-dept-config:create']" @click="handleEdit">新增</el-button>
        </template>
        <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
        <BasicTable @register="register" />

        <!-- 抽屉表单 -->
        <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
        <!-- 弹窗表单 -->
        <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
      </BasicPage>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { MedicineDeptConfigApi } from '@/api/system/smc/medicinedeptconfig'
import DeptTree from '@/views/system/user/DeptTree.vue'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:medicine-dept-config:update', callback: handleEdit },
    { label: '查看', permCode: 'system:medicine-dept-config:query', callback: handleDetail },
    { label: '删除', permCode: 'system:medicine-dept-config:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: MedicineDeptConfigApi.getMedicineDeptConfigPage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    const searchParams = { ...params, ...searchForm.value }

    // 处理创建时间搜索，结束日期设置为23:59:59
    if (searchParams.createTime && Array.isArray(searchParams.createTime) && searchParams.createTime.length === 2) {
      const [startDate, endDate] = searchParams.createTime
      if (startDate) {
        // 开始日期设置为00:00:00
        searchParams.createTime[0] = startDate.split(' ')[0] + ' 00:00:00'
      }
      if (endDate) {
        // 结束日期设置为23:59:59
        searchParams.createTime[1] = endDate.split(' ')[0] + ' 23:59:59'
      }
    }

    return searchParams
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}

/** 处理部门被点击 */
const handleDeptNodeClick = async (row: { [key: string]: any }) => {
  searchForm.value.deptId = row.id
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: MedicineDeptConfigApi.getMedicineDeptConfig,
    submitApi: id ? MedicineDeptConfigApi.updateMedicineDeptConfig : MedicineDeptConfigApi.createMedicineDeptConfig,
    beforeSubmit: (formData: any) => {
      // 确保规格和单位字段传递固定值到接口
      return {
        ...formData,
        // specification: '1mg',
        // unit: '支'
      }
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await MedicineDeptConfigApi.deleteMedicineDeptConfig(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>