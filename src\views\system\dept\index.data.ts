import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '部门名称',
      prop: 'name'
    },
    {
      component: 'input',
      label: '部门编码',
      prop: 'deptCode'
    },
    {
      component: 'select',
      label: '部门属性',
      prop: 'deptType',
      options: getIntDictOptions(DICT_TYPE.SYSTEM_DEPT_TYPE)
    },
    {
      component: 'select',
      label: '部门状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    { label: '部门名称', prop: 'name', minWidth: 180, tooltip: true },
    { label: '部门编码', prop: 'deptCode', minWidth: 180, tooltip: true },
    { label: '部门属性', prop: 'deptType', minWidth: 180, tooltip: true,
      dictType: DICT_TYPE.SYSTEM_DEPT_TYPE },
    { label: '部门经理', slot: 'leader', minWidth: 120, tooltip: true },
    { label: '排序', prop: 'sort', minWidth: 60 },
    { label: '备注', prop: 'remark', minWidth: 140, tooltip: true },
    {
      label: '状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        label: '上级部门',
        slot: 'parentId',
        rules: [{ required: true, message: '上级部门不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '部门名称',
        prop: 'name',
        rules: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '部门编码',
        prop: 'deptCode',
        rules: [{ required: true, message: '部门编码不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '部门属性',
        prop: 'deptType',
        options: getIntDictOptions(DICT_TYPE.SYSTEM_DEPT_TYPE),
        rules: [{ required: true, message: '部门属性不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '显示排序',
        prop: 'sort',
        rules: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }]
      },
      {
        label: '部门经理',
        slot: 'leaderUserId',
        rules: [{ required: true, message: '部门经理不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '联系电话',
        prop: 'phone',
      },
      {
        component: 'input',
        label: '邮箱',
        prop: 'email',
      },
      {
        component: 'select',
        label: '状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      }
    ]
  }
])
