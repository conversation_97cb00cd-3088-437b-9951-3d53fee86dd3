import request from '@/config/axios'

// 药柜抽屉信息 VO
export interface CabinetDrawerVO {
  id: number // 主键id
  cabinetId: number // 所属药柜ID
  drawerCode: string // 抽屉物理 ID
  ip: string // IP地址
  port: string // 地址端口
  drawerName: string // 抽屉名称
  drawerType: string // 抽屉类型:4格/9格/16格
  drawerUsage: string // 抽屉用途:1药格/10回收格
  cabinetBody: string // 所属柜体:1主柜/10扩展柜
  position: string // 在药柜中的位置
  drawerStatus: string // 抽屉状态:online/offline
  slotCount: number // 格口数 
  slotVolume: number // 格口容积(立方米)
  safetyVolumeThreshold: number // 安全容积阈值(0-100)
  temperature: string // 温度
  humidity: string // 湿度
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 药柜抽屉信息 API
export const CabinetDrawerApi = {
  // 查询药柜抽屉信息分页
  getCabinetDrawerPage: async (params: any) => {
    return await request.get({ url: `/system/cabinet-drawer/page`, params })
  },
  // 查询药柜抽屉信息列表
  getCabinetDrawerList: async (params) => {
    return await request.get({ url: `/system/cabinet-drawer/list`, params })
  },

  // 查询药柜抽屉信息详情
  getCabinetDrawer: async (params: any) => {
    return await request.get({ url: `/system/cabinet-drawer/get`, params })
  },

  // 新增药柜抽屉信息
  createCabinetDrawer: async (data: CabinetDrawerVO) => {
    return await request.post({ url: `/system/cabinet-drawer/create`, data })
  },

  // 修改药柜抽屉信息
  updateCabinetDrawer: async (data: CabinetDrawerVO) => {
    return await request.put({ url: `/system/cabinet-drawer/update`, data })
  },

  // 删除药柜抽屉信息
  deleteCabinetDrawer: async (id: number) => {
    return await request.delete({ url: `/system/cabinet-drawer/delete?id=` + id })
  },

  // 导出药柜抽屉信息 Excel
  exportCabinetDrawer: async (params) => {
    return await request.download({ url: `/system/cabinet-drawer/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/cabinet-drawer/get-import-template` })
  },

  // 导入药柜抽屉信息 Excel
  importCabinetDrawer: async (formData) => {
    return await request.upload({ url: `/system/cabinet-drawer/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/cabinet-drawer/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}