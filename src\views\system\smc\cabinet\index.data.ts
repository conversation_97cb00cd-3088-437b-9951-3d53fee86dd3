import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
import { DeptApi } from "@/api/system/dept/deptOption";

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '药柜编号',
      prop: 'cabinetCode',
      placeholder: '请输入药柜编号'
    },
    {
      component: 'input',
      label: '存放位置',
      prop: 'location',
      placeholder: '请输入存放位置'
    },
    // {
    //   component: 'input',
    //   label: 'IP地址',
    //   prop: 'ip',
    //   placeholder: '请输入IP地址'
    // },
    {
      component: 'select',
      label: '在离线状态',
      prop: 'onlineStatus',
      placeholder: '请选择在离线状态',
      params: {},
      filterable: true,
      // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_ONLINE_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '最后心跳时间',
      prop: 'lastHeartbeat',
      type: 'daterange',
      startPlaceholder: '最后心跳时间开始日期',
      endPlaceholder: '最后心跳时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },

  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '药柜编号',
      prop: 'cabinetCode',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '存放位置',
      prop: 'location',
      tooltip: true,
      minWidth: 100
    },
    {
      label: 'IP地址',
      prop: 'ip',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '最后心跳时间',
      prop: 'lastHeartbeat',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '在离线状态',
      prop: 'onlineStatus',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_ONLINE_STATUS, row.onlineStatus)
    },
    {
      label: '药格数',
      prop: 'drugSlotCount',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '回收格数',
      prop: 'recycleSlotCount',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '当前存放药品数',
      prop: 'currentMedicineCount',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '可使用部门',
      prop: 'deptIdsName',
      tooltip: true,
      minWidth: 150
    },
    // {
    //   label: '备注',
    //   prop: 'remark',
    //   tooltip: true,
    //   minWidth: 100
    // },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '药柜设备信息',
    items: [
      {
        component: 'input',
        label: '药柜编号',
        prop: 'cabinetCode',
        placeholder: '请输入药柜编号',
        rules: [
          { required: true, message: '药柜编号不能为空', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '存放位置',
        prop: 'location',
        placeholder: '请输入存放位置',
        rules: [
          { required: true, message: '存放位置不能为空', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: 'IP地址',
        prop: 'ip',
        placeholder: '请输入IP地址',
        rules: [
          { required: true, message: 'IP地址不能为空', trigger: 'blur' }
        ]
      },
      // {
      //   component: 'number',
      //   label: '药格数',
      //   prop: 'drugSlotCount',
      //   max: 9999999999,
      //   placeholder: '请输入药格数',
      // },
      // {
      //   component: 'number',
      //   label: '回收格数',
      //   prop: 'recycleSlotCount',
      //   max: 9999999999,
      //   placeholder: '请输入回收格数',
      // },
      // {
      //   component: 'number',
      //   label: '当前存放药品数',
      //   prop: 'currentMedicineCount',
      //   max: 9999999999,
      //   placeholder: '请输入当前存放药品数',
      // },
      {
        component: 'select',
        label: '可使用部门',
        prop: 'deptIds',
        placeholder: '请选择可使用部门',
        filterable: true,
        params: {},
        multiple: true,
        propName: 'deptIdsName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: DeptApi.getDeptList,
        rules: [
          { required: true, message: '可使用部门不能为空', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '硬件版本号',
        prop: 'hardwareVersion',
        placeholder: '请输入硬件版本号',
        rules: [
          { required: true, message: '硬件版本号不能为空', trigger: 'blur' }
        ]
      },
      // {
      //   component: 'input',
      //   label: '备注',
      //   prop: 'remark',
      //   placeholder: '请输入备注',
      //   rules: [
      //     { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
      //   ]
      // },
    ]
  }
])
