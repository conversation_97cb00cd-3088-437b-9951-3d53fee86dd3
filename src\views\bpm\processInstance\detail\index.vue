<template>
  <BasicPage v-model:tab-index="tabIndex" back :tabs="['审批任务', '审批记录', '流程图']">
    <div v-if="tabIndex === 0" class="sub-card no-border">
      <BasicBlockTitle :title="`申请信息(${processInstance.approvalNo || '--'})`" :size="16" />
      <div class="sub-card sub-card-detail">
        <!-- 情况一：流程表单 -->
        <el-col v-if="processInstance?.processDefinition?.formType === 10" :offset="6" :span="16">
          <form-create v-model="detailForm.value" v-model:api="fApi" :option="detailForm.option" :rule="detailForm.rule" />
        </el-col>
        <!-- 情况二：业务表单 -->
        <div v-loading="processInstanceLoading" class="business-form">
          <div v-if="processInstance?.processDefinition?.formType === 20">
            <BusinessFormComponent :id="processInstance.businessKey" type="view" />
          </div>
        </div>
      </div>

      <BasicBlockTitle title="审批任务" :size="16" />
      <div class="sub-card sub-card-detail">
        <el-empty v-if="runningTasks.length === 0" description="当前无审批任务" />
        <div v-for="(item, index) in runningTasks" :key="index" v-loading="processInstanceLoading" class="sub-task">
          <BasicBlockTitle :title="item.name" />
          <el-form :ref="'form' + index" :model="auditForms[index]" :rules="auditRule" label-position="top" label-width="100px">
            <el-form-item v-if="processInstance && processInstance.name" label="流程名:">
              <span class="view">{{ processInstance.name }}</span>
            </el-form-item>
            <el-form-item v-if="processInstance && processInstance.startUser" label="流程发起人:">
              <span class="view">
                {{ processInstance?.startUser.nickname }}
                <el-tag class="ml-6px" size="small" type="info">
                  {{ processInstance?.startUser.deptName }}
                </el-tag>
              </span>
            </el-form-item>
            <el-card v-if="runningTasks[index].formId > 0" class="mb-15px !-mt-10px">
              <template #header>
                <span class="el-icon-picture-outline">填写表单【{{ runningTasks[index]?.formName }}】</span>
              </template>
              <form-create
                v-model="approveForms[index].value"
                v-model:api="approveFormFApis[index]"
                :option="approveForms[index].option"
                :rule="approveForms[index].rule"
              />
            </el-card>
            <el-form-item label="审批意见:" prop="reason">
              <el-input v-model="auditForms[index].reason" placeholder="请输入审批意见" type="textarea" />
            </el-form-item>
            <el-form-item label="抄送人:" prop="copyUserIds">
              <el-select v-model="auditForms[index].copyUserIds" multiple placeholder="请选择抄送人">
                <el-option v-for="subItem in userOptions" :key="subItem.id" :label="subItem.nickname" :value="subItem.id" />
              </el-select>
            </el-form-item>
          </el-form>
          <div class="sub-task-butotns">
            <el-button type="success" :loading="btnLoading1" @click="handleAudit(item, true)">通过</el-button>
            <el-button type="danger" :loading="btnLoading2" @click="handleAudit(item, false)">不通过</el-button>
            <el-button type="primary" @click="openTaskUpdateAssigneeForm(item.id)">转办</el-button>
            <el-button type="primary" @click="handleDelegate(item)">委派</el-button>
            <el-button type="primary" @click="handleSign(item)">加签</el-button>
            <el-button type="warning" @click="handleBack(item)">回退</el-button>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="tabIndex === 1" class="sub-card">
      <ProcessInstanceTaskList :loading="tasksLoad" :process-instance="processInstance" :tasks="tasks" @refresh="getTaskList" />
    </div>
    <div v-else class="sub-card !p-0">
      <ProcessInstanceBpmnViewer
        :id="`${id}`"
        :bpmn-xml="bpmnXml"
        :loading="processInstanceLoading"
        :process-instance="processInstance"
        :tasks="tasks"
      />
    </div>
  </BasicPage>
  <!-- 弹窗：转派审批人 -->
  <TaskTransferForm ref="taskTransferFormRef" @success="getDetail" />
  <!-- 弹窗：回退节点 -->
  <TaskReturnForm ref="taskReturnFormRef" @success="getDetail" />
  <!-- 弹窗：委派，将任务委派给别人处理，处理完成后，会重新回到原审批人手中-->
  <TaskDelegateForm ref="taskDelegateForm" @success="getDetail" />
  <!-- 弹窗：加签，当前任务审批人为A，向前加签选了一个C，则需要C先审批，然后再是A审批，向后加签B，A审批完，需要B再审批完，才算完成这个任务节点 -->
  <TaskSignCreateForm ref="taskSignCreateFormRef" @success="getDetail" />
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { setConfAndFields2 } from '@/utils/formCreate'
import type { ApiAttrs } from '@form-create/element-ui/types/config'
import * as DefinitionApi from '@/api/bpm/definition'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import * as TaskApi from '@/api/bpm/task'
import ProcessInstanceBpmnViewer from './ProcessInstanceBpmnViewer.vue'
import ProcessInstanceTaskList from './ProcessInstanceTaskList.vue'
import TaskReturnForm from './dialog/TaskReturnForm.vue'
import TaskDelegateForm from './dialog/TaskDelegateForm.vue'
import TaskTransferForm from './dialog/TaskTransferForm.vue'
import TaskSignCreateForm from './dialog/TaskSignCreateForm.vue'
import { registerComponent } from '@/utils/routerHelper'
import { isEmpty } from '@/utils/is'
import * as UserApi from '@/api/system/user'
import { throttle } from 'lodash-es'
import { ProjectBaseInfoApi } from '@/api/pmis/project/basic'
// import { useApproveStore } from '@/store/modules/approve'

defineOptions({ name: 'BpmProcessInstanceDetail' })

const { query } = useRoute() // 查询参数
const router = useRouter()
const message = useMessage() // 消息弹窗
const { proxy } = getCurrentInstance() as any
// const approveStore = useApproveStore()

const btnLoading1 = ref(false)
const btnLoading2 = ref(false)
const tabIndex = ref(0)
const userId = useUserStore().getUser.id // 当前登录的编号
const id = query.id as unknown as string // 流程实例的编号
const processInstanceLoading = ref(false) // 流程实例的加载中
const processInstance = ref<any>({}) // 流程实例
const bpmnXml = ref('') // BPMN XML
const tasksLoad = ref(true) // 任务的加载中
const tasks = ref<any[]>([]) // 任务列表
// ========== 审批信息 ==========
const runningTasks = ref<any[]>([]) // 运行中的任务
const auditForms = ref<any[]>([]) // 审批任务的表单
const auditRule = reactive({
  reason: [{ required: true, message: '审批建议不能为空', trigger: 'blur' }]
})
const approveForms = ref<any[]>([]) // 审批通过时，额外的补充信息
const approveFormFApis = ref<ApiAttrs[]>([]) // approveForms 的 fAPi

// ========== 申请信息 ==========
const fApi = ref<ApiAttrs>() //
const detailForm = ref({
  rule: [],
  option: {},
  value: {}
}) // 流程实例的表单详情

/** 监听 approveFormFApis，实现它对应的 form-create 初始化后，隐藏掉对应的表单提交按钮 */
watch(
  () => approveFormFApis.value,
  (value) => {
    value?.forEach((api) => {
      api.btn.show(false)
      api.resetBtn.show(false)
    })
  },
  {
    deep: true
  }
)

/** 处理审批通过和不通过的操作 */
const handleAudit = throttle(async (task, pass) => {
  // 1.1 获得对应表单
  const index = runningTasks.value.indexOf(task)
  const auditFormRef = proxy.$refs['form' + index][0]
  // 1.2 校验表单
  const elForm = unref(auditFormRef)
  if (!elForm) return
  const valid = await elForm.validate()
  if (!valid) return

  // 2.1 提交审批
  if (pass) {
    btnLoading1.value = true
  } else {
    btnLoading2.value = true
  }
  const data = {
    id: task.id,
    reason: auditForms.value[index].reason,
    copyUserIds: auditForms.value[index].copyUserIds,
    variables: null
  }
  try {
    if (pass) {
      // 审批通过，并且有额外的 approveForm 表单，需要校验 + 拼接到 data 表单里提交
      const formCreateApi = approveFormFApis.value[index]
      if (formCreateApi) {
        await formCreateApi.validate()
        data.variables = approveForms.value[index].value
      }
      await TaskApi.approveTask(data)
      message.success('审批通过成功')
    } else {
      await TaskApi.rejectTask(data)
      message.success('审批不通过成功')
    }
    // 2.2 加载最新数据
    getDetail()
  } finally {
    if (pass) {
      btnLoading1.value = true
    } else {
      btnLoading2.value = true
    }
  }
}, 1000)

/** 转派审批人 */
const taskTransferFormRef = ref()
const openTaskUpdateAssigneeForm = (id: string) => {
  taskTransferFormRef.value.open(id)
}

/** 处理审批退回的操作 */
const taskDelegateForm = ref()
const handleDelegate = async (task) => {
  taskDelegateForm.value.open(task.id)
}

/** 处理审批退回的操作 */
const taskReturnFormRef = ref()
const handleBack = async (task: any) => {
  taskReturnFormRef.value.open(task.id)
}

/** 处理审批加签的操作 */
const taskSignCreateFormRef = ref()
const handleSign = async (task: any) => {
  taskSignCreateFormRef.value.open(task.id)
}

/** 获得详情 */
const getDetail = () => {
  // 1. 获得流程实例相关
  getProcessInstance()
  // 2. 获得流程任务列表（审批记录）
  getTaskList()
}

/** 加载流程实例 */
const BusinessFormComponent: any = ref(null) // 异步组件
const getProcessInstance = async () => {
  try {
    processInstanceLoading.value = true
    const data: any = await ProcessInstanceApi.getProcessInstance(id)
    if (!data) {
      message.error('查询不到流程信息！')
      return
    }
    processInstance.value = data

    // 获取流程单号
    const processKey = `${data.businessKey}_${data.processDefinition.key}`
    const requestData = { [processKey]: data.processDefinition.key }
    const processFormInfoRes = await ProjectBaseInfoApi.getProcessFormInfo({ params: requestData })
    const obj = processFormInfoRes[processKey]
    processInstance.value.approvalNo = obj.processFormInfo.approvalNo

    // 设置表单信息
    const processDefinition = data.processDefinition
    if (processDefinition.formType === 10) {
      setConfAndFields2(detailForm, processDefinition.formConf, processDefinition.formFields, data.formVariables)
      nextTick().then(() => {
        fApi.value?.btn.show(false)
        fApi.value?.resetBtn.show(false)
        fApi.value?.disabled(true) // TOTO: 是不是有问题？ wangrongxin
      })
    } else {
      // 注意：data.processDefinition.formCustomViewPath 是组件的全路径，例如说：/crm/contract/detail/index.vue
      BusinessFormComponent.value = registerComponent(data.processDefinition.formCustomViewPath)
    }

    // 加载流程图
    bpmnXml.value = (await DefinitionApi.getProcessDefinition(processDefinition.id as number))?.bpmnXml
  } finally {
    processInstanceLoading.value = false
  }
}

/** 加载任务列表 */
const getTaskList = async () => {
  runningTasks.value = []
  auditForms.value = []
  approveForms.value = []
  approveFormFApis.value = []
  try {
    // 获得未取消的任务
    tasksLoad.value = true
    const data = await TaskApi.getTaskListByProcessInstanceId(id)
    tasks.value = []
    // 1.1 移除已取消的审批
    data.forEach((task) => {
      if (task.status !== 4) {
        tasks.value.push(task)
      }
    })
    // 1.2 排序，将未完成的排在前面，已完成的排在后面；
    tasks.value.sort((a, b) => {
      // 有已完成的情况，按照完成时间倒序
      if (a.endTime && b.endTime) {
        return b.endTime - a.endTime
      } else if (a.endTime) {
        return 1
      } else if (b.endTime) {
        return -1
        // 都是未完成，按照创建时间倒序
      } else {
        return b.createTime - a.createTime
      }
    })

    // 获得需要自己审批的任务
    loadRunningTask(tasks.value)
  } finally {
    tasksLoad.value = false
  }
}

/**
 * 设置 runningTasks 中的任务
 */
const loadRunningTask = (tasks) => {
  tasks.forEach((task) => {
    if (!isEmpty(task.children)) {
      loadRunningTask(task.children)
    }
    // 2.1 只有待处理才需要
    if (task.status !== 1 && task.status !== 6) {
      return
    }
    // 2.2 自己不是处理人
    if (!task.assigneeUser || task.assigneeUser.id !== userId) {
      return
    }
    // 2.3 添加到处理任务
    runningTasks.value.push({ ...task })
    auditForms.value.push({
      reason: '',
      copyUserIds: []
    })

    // 2.4 处理 approve 表单
    if (task.formId && task.formConf) {
      const approveForm = {}
      setConfAndFields2(approveForm, task.formConf, task.formFields, task.formVariable)
      approveForms.value.push(approveForm)
    } else {
      approveForms.value.push({}) // 占位，避免为空
    }
  })
}

// const backFn = () => {
//   router.back()
// }

/** 初始化 */
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
onMounted(async () => {
  getDetail()
  // 获得用户列表
  userOptions.value = await UserApi.getSimpleUserList()
})
</script>

<style lang="scss" scoped>
.sub-card {
  margin: 26px 0;
  padding: 20px;
  border: 1px solid #e4e4e4;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.no-border {
  padding: 0;
  border: none;
}
.sub-card-detail {
  margin: 14px 0;
  padding: 14px;
}
.sub-task {
  width: 800px;
  .el-form {
    margin-left: 10px;
    margin-top: 14px;
    .view {
      width: 100%;
      background-color: rgb(242, 243, 245);
      padding: 0 8px;
    }
    .el-form-item {
      margin-bottom: 20px;
    }
  }
  .sub-task-butotns {
    margin: 20px 12px 0;
    font-size: 14px;
    .el-button {
      width: 100px;
    }
  }
}
.business-form {
  min-height: 300px;
}
</style>
