<template>
  <!-- :visible="visible" -->
  <el-popover
    placement="right-start"
    :popper-style="{ padding: '0', maxHeight: '400px', overflowY: 'hidden' }"
    :width="240"
    :show-arrow="false"
    trigger="click"
    :teleported="false"
  >
    <template #reference>
      <!-- @click="visible = !visible" -->
      <el-button text class="tool-btn">
        <Icon icon="ep:operation" />
      </el-button>
    </template>
    <div class="popver-content">
      <el-row justify="space-between" align="middle" class="tool">
        <el-checkbox v-model="showIndex">序号</el-checkbox>
        <el-button text type="primary" class="!px-10px" :icon="Refresh" @click="handleReset">重置</el-button>
      </el-row>
      <!-- <div class="column-config"> -->
      <draggable
        class="draggable"
        item-key="label"
        :list="configColumns"
        ghost-class="_ghost"
        chosen-class="_chosenClass"
        animation="300"
        @end="updateColumns"
      >
        <template #item="{ element }">
          <el-checkbox-group :model-value="checkBoxGroup">
            <el-row align="middle" class="column-config-item">
              <Icon icon="ep:rank" :size="14" />
              <div class="line"></div>
              <el-checkbox :label="element.label" :value="element.label" class="checkbox" @click="handleSelect(element)" />
            </el-row>
          </el-checkbox-group>
        </template>
      </draggable>
      <!-- </div> -->
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import draggable from 'vuedraggable'
import { Refresh } from '@element-plus/icons-vue'

const emit = defineEmits(['update'])

const props = defineProps({
  columns: {
    type: Array as PropType<any[]>,
    required: true,
    default: () => []
  }
})

// const visible = ref(false)
const checkBoxGroup: any = ref([]) // 复选框组
let originColumns: any = [] // 原始列
const configColumns: any = ref([]) // 配置列
const showIndex = ref(true) // 是否显示序号
const indexColumn: any = ref({})
const operationColumn: any = ref({})

const updateColumns = () => {
  const newColumns = configColumns.value.filter((i: any) => checkBoxGroup.value.includes(i.label))
  if (showIndex.value) {
    if (indexColumn.value) {
      newColumns.unshift(indexColumn.value)
    }
    if (operationColumn.value) {
      newColumns.push(operationColumn.value)
    }
  } else {
    if (operationColumn.value) {
      newColumns.push(operationColumn.value)
    }
  }
  emit('update', newColumns)
}

const handleSelect = (column: any) => {
  if (!checkBoxGroup.value.includes(column.label)) {
    checkBoxGroup.value.push(column.label)
  } else {
    checkBoxGroup.value = checkBoxGroup.value.filter((label: string) => !(label === column.label))
  }
  updateColumns()
}

const handleReset = () => {
  configColumns.value = originColumns.slice()
  showIndex.value = true
  checkBoxGroup.value = configColumns.value.map((i: any) => i.label)
  updateColumns()
}

watch(
  () => showIndex.value,
  () => updateColumns()
)

watch(
  () => props.columns,
  (list: any) => {
    // 原始列，重置的时候用
    originColumns = props.columns.filter((i: any) => ![i?.index, i?.operation, i?.selection].includes(true))
    // 过滤掉序号和操作的列
    configColumns.value = props.columns.filter((i: any) => ![i?.index, i?.operation, i?.selection].includes(true))
    // 复选框组
    checkBoxGroup.value = configColumns.value.map((i: any) => i.label)
    // 序号列
    indexColumn.value = list.find((i: any) => i.index === true)
    // 操作列
    operationColumn.value = list.find((i: any) => i.operation === true)
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.tool-btn {
  position: absolute;
  top: 7px;
  right: 10px;
  z-index: 10;
  padding: 0;
  background-color: white;
  width: 26px;
  height: 26px;
  box-shadow: 0 0 10px rgba($color: #194ff7, $alpha: 0.2);
}
.tool {
  height: 34px;
  margin: 4px 6px 4px 16px;
}
.draggable {
  max-height: 400px - 34px - 16px - 8px;
  padding: 0 8px 8px;
  overflow-y: auto;
  .column-config-item {
    cursor: pointer;
    border: 1px solid transparent;
    padding: 0 8px;
    color: #191919;
    :deep(.checkbox) {
      width: calc(100% - 28px - 8px);
      .el-checkbox__label {
        color: #191919;
        font-size: 15px;
      }
    }
    .line {
      width: 1px;
      height: 14px;
      margin: 0 8px;
      background-color: var(--el-border-color-lighter);
    }
  }
}

._drag-item {
  border: solid 1px var(--el-border-color-lighter);
  padding: 6px 10px;
  text-align: left;
}
._drag-item:hover {
  cursor: move;
}
._drag-item + ._drag-item {
  margin-top: 10px;
}
._ghost {
  border: 1px solid var(--el-color-primary);
}
._chosenClass {
  background-color: #f1f1f1;
}
</style>
