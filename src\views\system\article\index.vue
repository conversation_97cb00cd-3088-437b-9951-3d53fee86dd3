<template>
  <BasicPage :tabs="['新闻资讯管理']">
    <template #action>
      <BasicButtonImport
              perm-code="system:article:import"
              file-name="新闻资讯管理"
              :template-api="ArticleApi.importTemplate"
              :import-api="ArticleApi.importArticle"
              :exportError-file-api="ArticleApi.exportErrorFile"
              @success="handlerImportSuccess"
      />
      <BasicButtonExport
              perm-code="system:article:export"
              file-name="新闻资讯管理"
              :export-api="ArticleApi.exportArticle"
      />
      <el-button v-hasPermi="['system:article:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register"/>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { ArticleApi} from '@/api/system/article'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑',permCode: 'system:article:update', callback: handleEdit },
    { label: '查看',permCode: 'system:article:query', callback: handleDetail },
    { label: '删除',permCode: 'system:article:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: ArticleApi.getArticlePage,
  columns: totalColumns,
  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    console.log(searchForm.value)
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

const searchFn = (e: any) => {
  reload()
}

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: ArticleApi.getArticle,
    submitApi: id ? ArticleApi.updateArticle : ArticleApi.createArticle
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await ArticleApi.deleteArticle(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>
