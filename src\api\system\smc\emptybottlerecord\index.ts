import request from '@/config/axios'

// 空瓶回收记录 VO
export interface EmptyBottleRecordVO {
  id: number // 主键id
  cabinetId: number // 药柜ID
  medicineId: number // 药品ID
  documentDoctorMedicineId: number // 医生取(分)药品批次关联ID
  batchNumber: string // 批次号
  takenQuantity: number // 当日取药数量
  usedQuantity: number // 使用数量
  returnedQuantity: number // 已退回药品数量
  recycledQuantity: number // 已回收空瓶数量
  collectedQuantity: number // 已取出空瓶数量
  recycleDate: Date // 回收日期
  deptId: number // 部门ID
  doctorId: number // 医生ID
  recycleOperator: string // 回收操作人
  collectOperator: string // 取出操作人
  collectTime: Date // 取出时间
  pendingRecycle: number // 待回收数量
  availableCollect: number // 可取空瓶数量
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 空瓶回收记录 API
export const EmptyBottleRecordApi = {
  // 查询空瓶回收记录分页
  getEmptyBottleRecordPage: async (params: any) => {
    return await request.get({ url: `/system/empty-bottle-record/page`, params })
  },
  // 查询空瓶回收记录列表
  getEmptyBottleRecordList: async (params) => {
    return await request.get({ url: `/system/empty-bottle-record/list`, params })
  },

  // 查询空瓶回收记录详情
  getEmptyBottleRecord: async (params: any) => {
    return await request.get({ url: `/system/empty-bottle-record/get`, params })
  },

  // 新增空瓶回收记录
  createEmptyBottleRecord: async (data: EmptyBottleRecordVO) => {
    return await request.post({ url: `/system/empty-bottle-record/create`, data })
  },

  // 修改空瓶回收记录
  updateEmptyBottleRecord: async (data: EmptyBottleRecordVO) => {
    return await request.put({ url: `/system/empty-bottle-record/update`, data })
  },

  // 删除空瓶回收记录
  deleteEmptyBottleRecord: async (id: number) => {
    return await request.delete({ url: `/system/empty-bottle-record/delete?id=` + id })
  },

  // 导出空瓶回收记录 Excel
  exportEmptyBottleRecord: async (params) => {
    return await request.download({ url: `/system/empty-bottle-record/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/empty-bottle-record/get-import-template` })
  },

  // 导入空瓶回收记录 Excel
  importEmptyBottleRecord: async (formData) => {
    return await request.upload({ url: `/system/empty-bottle-record/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/empty-bottle-record/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}