.el-form-item__label {
  font-size: 14px !important;
  color: #1d2129 !important;
  padding: 7px 6px 0 0px !important;
  line-height: 17px !important;
}

.el-input__inner {
  font-size: 14px !important;
  color: #1d2129 !important;
}

.el-form--default.el-form--label-top .el-form-item .el-form-item__label {
  width: fit-content !important;
  margin-bottom: 10px !important;
}

.el-form--label-top .el-form-item {
  margin-bottom: 14px !important;
}

/* 消除自动填充的input背景色 */
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.el-select__wrapper,
.el-select-v2__wrapper {
  background-color: rgb(242, 243, 245) !important;
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
}
.el-select__wrapper.is-hovering:not(.is-focused),
.el-select-v2__wrapper.is-hovering:not(.is-focused) {
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
  background-color: #dcdfe6 !important;
}

.el-tag.el-tag--info {
  background-color: white !important;
}

.el-input-group__append {
  box-shadow: none;
  background-color: rgb(242, 243, 245);
  .el-button {
    border-color: white !important;
    background-color: white !important;
    margin: 0 -16px;
    height: calc(100% - 6px);
    padding: 0 14px;
    transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    &:hover {
      background-color: #dcdfe6 !important;
    }
  }
}

.el-input__wrapper {
  background-color: rgb(242, 243, 245);
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
  transition: var(--el-transition-duration) !important;
  &:hover {
    background-color: #dcdfe6 !important;
  }
}

.el-range-editor.el-input__wrapper {
  background-color: rgb(242, 243, 245);
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
  transition: var(--el-transition-duration) !important;
  &:hover {
    background-color: #dcdfe6 !important;
  }
}

.el-textarea__inner {
  background-color: rgb(242, 243, 245);
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
  transition: background-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  &:hover {
    background-color: #dcdfe6 !important;
  }
}
.el-input__count {
  background-color: transparent !important;
}

.el-input-number__increase,
.el-input-number__decrease {
  background-color: white !important;
  border: none !important;
  margin: 2px !important;
  width: 24px !important;
  height: 12px !important;
  &:active {
    background-color: #dcdfe6 !important;
  }
}
