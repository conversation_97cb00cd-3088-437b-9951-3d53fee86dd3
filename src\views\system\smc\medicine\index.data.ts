import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '药品通用名称',
      prop: 'medicineName',
      placeholder: '请输入药品通用名称'
    },
    {
      component: 'input',
      label: '药品商品名称',
      prop: 'tradeName',
      placeholder: '请输入药品商品名称'
    },
    // {
    //   component: 'input',
    //   label: '国家药品标识码（唯一）',
    //   prop: 'nationalDrugCode',
    //   placeholder: '请输入国家药品标识码（唯一）'
    // },
    {
      component: 'input',
      label: '药品本位码',
      prop: 'drugBaseCode',
      placeholder: '请输入药品本位码'
    },
    // {
    //   component: 'input',
    //   label: '制剂规格',
    //   prop: 'specification',
    //   placeholder: '请输入制剂规格'
    // },
    // {
    //   component: 'input',
    //   label: '剂型',
    //   prop: 'dosageForm',
    //   placeholder: '请输入剂型'
    // },
    // {
    //   component: 'input',
    //   label: '单位',
    //   prop: 'unit',
    //   placeholder: '请输入单位'
    // },
    // {
    //   component: 'input',
    //   label: '包装规格',
    //   prop: 'packageSpecification',
    //   placeholder: '请输入包装规格'
    // },
    {
      component: 'datePickerRange',
      label: '同步时间',
      prop: 'syncTime',
      type: 'daterange',
      startPlaceholder: '同步时间开始日期',
      endPlaceholder: '同步时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    // {
    //   component: 'select',
    //   label: '是否已禁用 0=否（正常）,1=是（停用）',
    //   prop: 'status',
    //   placeholder: '请选择是否已禁用 0=否（正常）,1=是（停用）',
    //   params: {},
    //   filterable: true,
    //   options: getIntDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)
    // },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      type: 'daterange',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    // {
    //   label: '主键id',
    //   prop: 'id',
    //   tooltip: true,
    //   minWidth: 100
    // },
    {
      label: '药品通用名称',
      prop: 'medicineName',
      tooltip: true,
      minWidth: 120  // 根据表头文字长度调整
    },
    {
      label: '药品商品名称',
      prop: 'tradeName',
      tooltip: true,
      minWidth: 120  // 根据表头文字长度调整
    },
    {
      label: '国家药品标识码',
      prop: 'nationalDrugCode',
      tooltip: true,
      minWidth: 140  // 根据表头文字长度调整
    },
    {
      label: '药品本位码',
      prop: 'drugBaseCode',
      tooltip: true,
      minWidth: 110  // 根据表头文字长度调整
    },
    {
      label: '剂型',
      prop: 'dosageForm',
      tooltip: true,
      minWidth: 80   // 根据表头文字长度调整
    },
    {
      label: '制剂规格',
      prop: 'specification',
      tooltip: true,
      minWidth: 100  // 根据表头文字长度调整
    },
    {
      label: '包装规格',
      prop: 'packageSpecification',
      tooltip: true,
      minWidth: 100  // 根据表头文字长度调整
    },
    {
      label: '包装转换比',
      prop: 'packageConversionRatio',
      tooltip: true,
      minWidth: 110  // 根据表头文字长度调整
    },
    {
      label: '药品有效期(月)',
      prop: 'shelfLife',
      tooltip: true,
      minWidth: 130  // 根据表头文字长度调整
    },
    // {
    //   label: '单位',
    //   prop: 'unit',
    //   tooltip: true,
    //   minWidth: 100
    // },
    {
      label: '盒体体积(m³)',
      prop: 'boxVolume',
      tooltip: true,
      minWidth: 120  // 根据表头文字长度调整
    },
    {
      label: '盒装重量(g)',
      prop: 'boxWeight',
      tooltip: true,
      minWidth: 110  // 根据表头文字长度调整
    },
    {
      label: '单支重量(g)',
      prop: 'singleWeight',
      tooltip: true,
      minWidth: 110  // 根据表头文字长度调整
    },
    {
      label: '存取频率',
      prop: 'accessFrequency',
      tooltip: true,
      minWidth: 100  // 根据表头文字长度调整
    },
    // {
    //   label: '基数数量',
    //   prop: 'baseQuantity',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '预计使用天数',
    //   prop: 'estimatedDays',
    //   tooltip: true,
    //   minWidth: 100
    // },
    {
      label: '同步时间',
      prop: 'syncTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180  // 时间列需要更宽的空间
    },
    // {
    //   label: 'HIS同步的完整数据(JSON格式)',
    //   prop: 'hisSyncData',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '是否已禁用 0=否（正常）,1=是（停用）',
    //   prop: 'status',
    //   minWidth: 100,
    //   filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    // },
    // {
    //   label: '排序',
    //   prop: 'sort',
    //   tooltip: true,
    //   minWidth: 100
    // },
    // {
    //   label: '备注',
    //   prop: 'remark',
    //   tooltip: true,
    //   minWidth: 120  // 备注列可能内容较多，给更多空间
    // },
    // {
    //   label: '创建者',
    //   prop: 'creator',
    //   tooltip: true,
    //   minWidth: 100
    // },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

// 编辑表单配置 - 只包含可编辑字段
export const editFormConfig = ref([
  {
    title: '药品信息',
    items: [
      {
        component: 'input',
        label: '药品通用名称',
        prop: 'medicineName',
        placeholder: '请输入药品通用名称',
        disabled: true,  // 禁用不可修改
        rules: [
          { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '药品商品名称',
        prop: 'tradeName',
        placeholder: '请输入药品商品名称',
        disabled: true,  // 禁用不可修改
        rules: [
          { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '盒体体积(m³)',
        prop: 'boxVolume',
        max: 99999999,
        placeholder: '请输入盒体体积(m³)',
      },
      {
        component: 'number',
        label: '盒装重量(g)',
        prop: 'boxWeight',
        max: 9999999999,
        placeholder: '请输入盒装重量(g)',
      },
      {
        component: 'number',
        label: '单支重量(g)',
        prop: 'singleWeight',
        max: 9999999999,
        placeholder: '请输入单支重量(g)',
      },
      {
        component: 'input',
        label: '存取频率',
        prop: 'accessFrequency',
        placeholder: '请输入存取频率',
        rules: [
          { max: 65535, message: '长度不能超过65535个字符', trigger: 'blur' }
        ]
      }
    ]
  }
])

// 查看表单配置 - 包含所有字段，顺序与列表一致
export const viewFormConfig = ref([
  {
    title: '药品信息',
    items: [
      {
        component: 'input',
        label: '药品通用名称',
        prop: 'medicineName',
        disabled: true
      },
      {
        component: 'input',
        label: '药品商品名称',
        prop: 'tradeName',
        disabled: true
      },
      {
        component: 'input',
        label: '国家药品标识码',
        prop: 'nationalDrugCode',
        disabled: true
      },
      {
        component: 'input',
        label: '药品本位码',
        prop: 'drugBaseCode',
        disabled: true
      },
      {
        component: 'input',
        label: '剂型',
        prop: 'dosageForm',
        disabled: true
      },
      {
        component: 'input',
        label: '制剂规格',
        prop: 'specification',
        disabled: true
      },
      {
        component: 'input',
        label: '包装规格',
        prop: 'packageSpecification',
        disabled: true
      },
      {
        component: 'number',
        label: '包装转换比',
        prop: 'packageConversionRatio',
        disabled: true
      },
      {
        component: 'number',
        label: '药品有效期(月)',
        prop: 'shelfLife',
        disabled: true
      },
      {
        component: 'number',
        label: '盒体体积(m³)',
        prop: 'boxVolume',
        disabled: true
      },
      {
        component: 'number',
        label: '盒装重量(g)',
        prop: 'boxWeight',
        disabled: true
      },
      {
        component: 'number',
        label: '单支重量(g)',
        prop: 'singleWeight',
        disabled: true
      },
      {
        component: 'input',
        label: '存取频率',
        prop: 'accessFrequency',
        disabled: true
      },
      {
        component: 'input',
        label: '同步时间',
        prop: 'syncTime',
        disabled: true
      }
    ]
  }
])

// 保持向后兼容
export const formConfig = editFormConfig
