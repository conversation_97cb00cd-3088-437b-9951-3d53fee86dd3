import {
  DICT_TYPE,
  getIntDictOptions,
  getDictLabel,
} from '@/utils/dict'
import {ArticleTypeApi} from "@/api/system/articletype";

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '标题',
      prop: 'title',
      placeholder: '请输入标题'
    },
    {
      component: 'treeSelect',
      label: '所属分类',
      prop: 'articleTypeId',
      placeholder: '请选择所属分类',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: ArticleTypeApi.getArticleTypeList,
    },
    {
      component: 'input',
      label: '作者',
      prop: 'author',
      placeholder: '请输入作者'
    },
    {
      component: 'input',
      label: '来源',
      prop: 'originFrom',
      placeholder: '请输入来源'
    },
    {
      component: 'datePickerRange',
      label: '发布日期',
      prop: 'publicDate',
      startPlaceholder: '发布日期开始日期',
      endPlaceholder: '发布日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'select',
      label: '是否禁用',
      prop: 'status',
      placeholder: '请选择是否禁用',
      options: getIntDictOptions(DICT_TYPE.YES_OR_NO)
    },
    {
      component: 'select',
      label: '是否热门',
      prop: 'beHot',
      placeholder: '请选择是否热门',
      options: getIntDictOptions(DICT_TYPE.YES_OR_NO)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '标题',
      prop: 'title',
      minWidth: 80
    },
    {
      label: '所属分类',
      prop: 'articleTypeIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '作者',
      prop: 'author',
      minWidth: 80
    },
    {
      label: '来源',
      prop: 'originFrom',
      minWidth: 80
    },
    {
      label: '发布日期',
      prop: 'publicDate',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '简介',
      prop: 'description',
      tooltip: true,
      minWidth: 80
    },
    {
      label: '浏览次数',
      prop: 'views',
      minWidth: 80
    },
    {
      label: '主图文件',
      image: true,
      prop: 'mainImgIds',
      minWidth: 80
    },
    {
      label: '视频文件',
      file: true,
      prop: 'videoId',
      minWidth: 80
    },
    {
      label: '附件',
      file: true,
      prop: 'attachmentIds',
      minWidth: 80
    },
    {
      label: '是否禁用',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.YES_OR_NO, row.status)
    },
    {
      label: '是否热门',
      prop: 'beHot',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.YES_OR_NO, row.beHot)
    },
    {
      label: '排序',
      prop: 'sort',
      minWidth: 80
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 80
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '新闻资讯',
    items: [
      {
        component: 'input',
        label: '标题',
        prop: 'title',
        placeholder: '请输入标题',
          rules: [{ required: true, message: '标题不能为空', trigger: 'blur' }]
      },
      {
        component: 'treeSelect',
        label: '所属分类',
        prop: 'articleTypeId',
        placeholder: '请选择所属分类',
        filterable: true,
        params: {},
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        api: ArticleTypeApi.getArticleTypeList,
        rules: [{ required: true, message: '所属分类不能为空', trigger: 'change' }]
      },
      {
        component: 'input',
        label: '作者',
        prop: 'author',
        placeholder: '请输入作者',
      },
      {
        component: 'input',
        label: '来源',
        prop: 'originFrom',
        placeholder: '请输入来源',
      },
      {
        component: 'datePicker',
        label: '发布日期',
        prop: 'publicDate',
        placeholder: '选择发布日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'select',
        label: '是否热门',
        prop: 'beHot',
        placeholder: '请选择是否热门',
        options: getIntDictOptions(DICT_TYPE.YES_OR_NO),
      },
      {
        component: 'select',
        label: '是否禁用',
        prop: 'status',
        placeholder: '请选择是否禁用',
        options: getIntDictOptions(DICT_TYPE.YES_OR_NO),
      },
      {
        component: 'upload-file',
        label: '主图文件',
        prop: 'mainImgIds',
        fileType: ['jpg', 'jpeg', 'png'],
        allPermission: true,
        listType: 'picture',
        fileSize: 6,
      },
      {
        component: 'upload-file',
        label: '视频文件',
        prop: 'videoId',
        fileType: ['mp4', 'MP4'],
        allPermission: true,
        fileSize: 1,
      },
      {
        component: 'upload-file',
        label: '附件',
        prop: 'attachmentIds',
        allPermission: true,
      },
      {
        component: 'textarea',
        label: '简介',
        prop: 'description',
        placeholder: '请输入简介',
        showWordLimit: true,
        maxlength: 500,
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
      },
      {
        component:'editor',
        label: '详情内容',
        singleLine: true,
        prop: 'content',
        placeholder: '请输入详情内容',
      },
    ]
  }
])
