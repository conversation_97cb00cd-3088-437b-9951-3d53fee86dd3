import request from '@/config/axios'

// 单据 VO
export interface DocumentsVO {
  id: number // 主键id
  documentNo: string // 单据编号（唯一)
  origin: string // 单据来源 his-his系统，self-本系统
  intoDeptId: number // 入库部门ID
  outDeptId: number // 出库部门ID
  documentType: string // 单据类型 10 药库入库单 20 药库销毁单 30 药库报损单 40药库出库单 50药房出库单 60药房退回单 70 科室退回单 200-分药单 -210医生取药单 220-医生退药单
  syncTime: Date // 同步时间
  hisSyncData: string // HIS同步的完整数据(JSON格式)
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 单据 API
export const DocumentsApi = {
  // 查询单据分页
  getDocumentsPage: async (params: any) => {
    return await request.get({ url: `/system/documents/page`, params })
  },
  // 查询单据列表
  getDocumentsList: async (params) => {
    return await request.get({ url: `/system/documents/list`, params })
  },

  // 查询单据详情
  getDocuments: async (params: any) => {
    return await request.get({ url: `/system/documents/get`, params })
  },

  // 新增单据
  createDocuments: async (data: DocumentsVO) => {
    return await request.post({ url: `/system/documents/create`, data })
  },

  // 修改单据
  updateDocuments: async (data: DocumentsVO) => {
    return await request.put({ url: `/system/documents/update`, data })
  },

  // 删除单据
  deleteDocuments: async (id: number) => {
    return await request.delete({ url: `/system/documents/delete?id=` + id })
  },

  // 导出单据 Excel
  exportDocuments: async (params) => {
    return await request.download({ url: `/system/documents/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/documents/get-import-template` })
  },

  // 导入单据 Excel
  importDocuments: async (formData) => {
    return await request.upload({ url: `/system/documents/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/documents/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}