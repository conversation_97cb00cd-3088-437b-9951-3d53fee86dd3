import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
import {ArticleTypeApi} from "@/api/system/articletype";
import {ForTestTypeApi} from "@/api/system/fortesttype";

export const formSearchConfig = {
  itemList: [
    {
      component: 'treeSelect',
      label: '标题',
      prop: 'title',
      placeholder: '请选择标题',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: ArticleTypeApi.getArticleTypeList,
    },
    {
      component: 'treeSelect',
      label: '所属分类id',
      prop: 'articleTypeId',
      placeholder: '请选择所属分类id',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: ArticleTypeApi.getArticleTypeList,
    },
    {
      component: 'number',
      label: '浏览次数',
      prop: 'views',
      placeholder: '请输入浏览次数'
    },
    {
      component: 'number',
      label: '测试数字',
      prop: 'decialNum',
      placeholder: '请输入测试数字'
    },
    {
      component: 'select',
      label: '作者',
      prop: 'author',
      placeholder: '请选择作者',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: ForTestTypeApi.getForTestTypeList,
    },
    {
      component: 'select',
      label: '来源',
      prop: 'originFrom',
      placeholder: '请选择来源',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: ForTestTypeApi.getForTestTypeList,
    },
    {
      component: 'datePickerRange',
      label: '发布日期',
      prop: 'publicDate',
      startPlaceholder: '发布日期开始日期',
      endPlaceholder: '发布日期结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'input',
      label: '附件ID',
      prop: 'attachmentIds',
      placeholder: '请输入附件ID'
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'select',
      label: '是否热门 0=否,1=是',
      prop: 'beHot',
      placeholder: '请选择是否热门 0=否,1=是',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getIntDictOptions(DICT_TYPE.YES_OR_NO)
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '标题',
      prop: 'titleName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '所属分类id',
      prop: 'articleTypeIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '浏览次数',
      prop: 'views',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '视频文件id',
      file: true,
      prop: 'videoId',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '测试数字',
      prop: 'decialNum',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '主图文件ids',
      file: true,
      prop: 'mainImgIds',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '简介',
      prop: 'description',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '作者',
      prop: 'authorName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '来源',
      prop: 'originFromName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '发布日期',
      prop: 'publicDate',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '详情内容',
      prop: 'content',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '附件ID',
      prop: 'attachmentIds',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否热门 0=否,1=是',
      prop: 'beHot',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.YES_OR_NO, row.beHot)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '测试而已无用',
    items: [
      {
        component: 'treeSelect',
        label: '标题',
        prop: 'title',
        placeholder: '请选择标题',
        filterable: true,
        params: {},
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        multiple: true,
        propName: 'titleName',
        api: ArticleTypeApi.getArticleTypeList,
        rules: [{ required: true, message: '标题不能为空', trigger: 'blur' }]
      },
      {
        component: 'treeSelect',
        label: '所属分类id',
        prop: 'articleTypeId',
        placeholder: '请选择所属分类id',
        filterable: true,
        params: {},
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        propName: 'articleTypeIdName',
        api: ArticleTypeApi.getArticleTypeList,
      },
      {
        component: 'number',
        label: '浏览次数',
        prop: 'views',
        max: 9999999999,
        placeholder: '请输入浏览次数',
          rules: [{ required: true, message: '浏览次数不能为空', trigger: 'blur' }]
      },
      {
        component: 'upload-file',
        label: '视频文件id',
        prop: 'videoId',
        allPermission: true,
      },
      {
        component: 'number',
        label: '测试数字',
        prop: 'decialNum',
        max: 9999999999999,
        placeholder: '请输入测试数字',
          rules: [{ required: true, message: '测试数字不能为空', trigger: 'blur' }]
      },
      {
        component: 'upload-file',
        label: '主图文件ids',
        prop: 'mainImgIds',
        allPermission: true,
      },
      {
        component:'editor',
        singleLine: true,
        label: '简介',
        prop: 'description',
        placeholder: '请输入简介',
      },
      {
        component: 'select',
        label: '作者',
        prop: 'author',
        placeholder: '请选择作者',
        params: {},
        filterable: true,
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        multiple: true,
        propName: 'authorName',
                api: ForTestTypeApi.getForTestTypeList,
        rules: [{ required: true, message: '作者不能为空', trigger: 'change' }]
      },
      {
        component: 'select',
        label: '来源',
        prop: 'originFrom',
        placeholder: '请选择来源',
        params: {},
        filterable: true,
        propName: 'originFromName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
                api: ForTestTypeApi.getForTestTypeList,
      },
      {
        component: 'datePicker',
        label: '发布日期',
        prop: 'publicDate',
        placeholder: '选择发布日期',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component:'editor',
        singleLine: true,
        label: '详情内容',
        prop: 'content',
        placeholder: '请输入详情内容',
      },
      {
        component: 'input',
        label: '附件ID',
        prop: 'attachmentIds',
        placeholder: '请输入附件ID',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        max: 999,
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
      },
      {
        component: 'select',
        label: '是否热门 0=否,1=是',
        prop: 'beHot',
        placeholder: '请选择是否热门 0=否,1=是',
        params: {},
        filterable: true,
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        options: getIntDictOptions(DICT_TYPE.YES_OR_NO),
        rules: [{ required: true, message: '是否热门 0=否,1=是不能为空', trigger: 'change' }]
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
    ]
  }
])
