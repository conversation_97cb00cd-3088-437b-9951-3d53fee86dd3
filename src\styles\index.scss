@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';
@import './element-plus/index.scss';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* 去除upload组件过渡效果 */
.el-upload-list__item {
  transition: none !important;
}

/* 滚动样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, .3);
  border-radius: 4px;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.custom-dialog .el-dialog__body {
  max-height: 600px;
  overflow-y: auto;
}

.child-table {
  .el-table__placeholder {
    content: url(../assets/svgs/icon_table_expand.svg) !important;
    width: 16px !important;
    height: 16px !important;
    margin-right: 5px !important;
    opacity: 0;
  }
  .el-table__expand-icon {
    content: url(../assets/svgs/icon_table_expand.svg) !important;
    width: 16px !important;
    height: 16px !important;
    margin-right: 5px !important;
  }
  .expand-icon {
    &:nth-child(2) {
      .cell {
        display: flex;
        align-items: center;
      }
    }
  }
  .header-expand-icon {
    &:nth-child(2) {
      .cell {
        padding-left: 32px;
      }
    }
  }
}
