<template>
  <BasicPage :tabs="['流程任务']">
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as TaskApi from '@/api/bpm/task'
import { formatPast2 } from '@/utils/formatTime'

defineOptions({ name: 'BpmManagerTask' })

const { push } = useRouter() // 路由

const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [{ label: '历史', permCode: '', callback: handleAudit }]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: TaskApi.getTaskManagerPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  },
  afterFetch: (res) => {
    res.forEach((item: any) => {
      item.processInstanceName = item.processInstance.name
      item.assigneeUserName = item.assigneeUser.nickname
      item.nickname = item.processInstance.startUser.nickname
      item.durationInMillis = formatPast2(item.durationInMillis)
    })
  }
})

/** 处理审批按钮 */
function handleAudit(row: any) {
  push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}

const searchFn = () => {
  reload()
}
</script>

<style></style>
