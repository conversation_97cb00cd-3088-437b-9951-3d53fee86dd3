<template>
  <BasicPage :tabs="['抄送我的']">
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'

defineOptions({ name: 'BpmProcessInstanceCopy' })

const router = useRouter()

const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 100,
  buttons: [{ label: '详情', permCode: '', callback: handleDetail }]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: ProcessInstanceApi.getProcessInstanceCopyPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  },
  afterFetch: (res) => {
    res.forEach((item: any) => {
      item.processInstanceName = item.processInstance.name
      item.nickname = item.processInstance.startUser.nickname
    })
  }
})

const searchFn = () => {
  reload()
}

/** 查看历史 */
function handleDetail(row) {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id:row.processInstanceId
    }
  })
}
</script>

<style></style>
