import { store } from '@/store'
import { defineStore } from 'pinia'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

interface ProjectVO {
  nodeInfoList: any
  currentBusinessInfo: any
  categoryList: any
  deptList: any
  userList: any
  projectInfo: any
  detailInfo: any
}

export const useProjectStore = defineStore('project', {
  state: (): ProjectVO => ({
    nodeInfoList: {},
    currentBusinessInfo: {},
    categoryList: [],
    deptList: [],
    userList: [],
    projectInfo: {},
    detailInfo: [
      // {
      //   key: 'pmis_project_contract',
      //   data: {}
      // }
    ]
  }),
  getters: {
    getNodeInfoList(): any {
      return wsCache.get(CACHE_KEY.PROJECT_NODE_INFO_LIST) || this.nodeInfoList
    },
    getCurrentBusinessInfo(): any {
      return wsCache.get(CACHE_KEY.CURRENT_BUSINESS_INFO) || this.currentBusinessInfo
    },
    getCategoryList(): any {
      return this.categoryList
    },
    getDeptList(): any {
      return this.deptList
    },
    getUsersList(): any {
      return this.userList
    },
    getProjectInfo(): any {
      return wsCache.get(CACHE_KEY.PROJECT_INFO) || this.projectInfo
    }
  },
  actions: {
    setNodeInfoList(data: any) {
      if (data) {
        wsCache.set(CACHE_KEY.PROJECT_NODE_INFO_LIST, data)
        this.nodeInfoList = data
        return
      }
      wsCache.delete(CACHE_KEY.PROJECT_NODE_INFO_LIST)
    },
    setCurrentBusinessInfo(data: any) {
      if (data) {
        wsCache.set(CACHE_KEY.CURRENT_BUSINESS_INFO, data)
        this.currentBusinessInfo = data
        return
      }
      wsCache.delete(CACHE_KEY.CURRENT_BUSINESS_INFO)
    },
    setCategoryList(data: any) {
      this.categoryList = data
    },
    setDeptList(data: any) {
      this.deptList = data
    },
    setUsersList(data: any) {
      this.userList = data
    },
    setProjectInfo(data: any) {
      if (data) {
        wsCache.set(CACHE_KEY.PROJECT_INFO, data)
        this.projectInfo = data
        return
      }
      wsCache.delete(CACHE_KEY.PROJECT_INFO)
    },
  }
})

export const useProjectStoreWithOut = () => {
  return useProjectStore(store)
}
