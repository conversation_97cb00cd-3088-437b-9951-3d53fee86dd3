export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '流程名称',
      prop: 'name'
    },
    {
      component: 'datePickerRange',
      label: '抄送时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '抄送时间开始',
      endPlaceholder: '抄送时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '流程名', prop: 'processInstanceName', minWidth: 140, tooltip: true },
    { label: '流程发起人', prop: 'startUserName', minWidth: 100, tooltip: true },
    { label: '流程发起时间', prop: 'processInstanceStartTime', minWidth: 180, tooltip: true },
    { label: '抄送任务', prop: 'taskName', minWidth: 180, tooltip: true },
    { label: '抄送人', prop: '抄送人', minWidth: 100, tooltip: true },
    { label: '抄送时间', prop: 'createTime', minWidth: 180, tooltip: true }
  ]
})
