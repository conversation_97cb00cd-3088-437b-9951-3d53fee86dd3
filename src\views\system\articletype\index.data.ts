import {
  DICT_TYPE,
  getIntDictOptions,
  getDictLabel,
} from '@/utils/dict'
import {ArticleTypeApi} from "@/api/system/articletype";

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '名称',
      prop: 'name',
      placeholder: '请输入名称'
    },
    {
      component: 'select',
      label: '是否已禁用',
      prop: 'status',
      placeholder: '请选择是否已禁用',
      options: getIntDictOptions(DICT_TYPE.YES_OR_NO)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '名称',
      prop: 'name',
      minWidth: 80
    },
    {
      label: '父类id',
      prop: 'parentIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '分类图标',
      image: true,
      prop: 'iconUrl',
      minWidth: 80
    },
    {
      label: '是否已禁用',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.YES_OR_NO, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      minWidth: 80
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 80
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '新闻资讯类型',
    items: [

      {
        component: 'input',
        label: '名称',
        prop: 'name',
        placeholder: '请输入名称',
          rules: [{ required: true, message: '名称不能为空', trigger: 'blur' }]
      },
      {
        component: 'treeSelect',
        label: '父类',
        prop: 'parentId',
        placeholder: '请选择所属父类',
        params: {},
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        api: ArticleTypeApi.getArticleTypeList,
        // rules: [{ required: true, message: '所属分类不能为空', trigger: 'change' }]
      },
      {
        component: 'upload-file',
        listType: 'picture',
        label: '分类图标',
        prop: 'iconUrl',
        allPermission: true,
        placeholder: '请输入分类图标',
        fileSize: 1,
      },
      {
        component: 'select',
        label: '是否已禁用',
        prop: 'status',
        placeholder: '请选择是否已禁用',
          options: getIntDictOptions(DICT_TYPE.YES_OR_NO),
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
      },
    ]
  }
])
