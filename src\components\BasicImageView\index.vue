<template>
  <template v-if="fileInfoList.length">
    <template v-if="inTable">
      <el-image v-for="(item, index) in fileInfoList" :key="index" :src="item" :preview-src-list="fileInfoList"
        :initial-index="index" preview-teleported :fit="fit"
        :style="{ width: computedWidth, height: computedHeight, top: index + 'px', left: index * 4 + 'px', position: 'absolute', borderRadius: computedBorderRadius, }" />
    </template>
    <template v-else>
      <el-image v-for="(item, index) in fileInfoList" :key="index" :src="item" :preview-src-list="fileInfoList"
        :initial-index="index" preview-teleported :fit="fit"
        :style="{ width: computedWidth, height: computedHeight, borderRadius: computedBorderRadius, }" />
    </template>
  </template>
  <template v-else>
    <template v-if="props.errorShow === 'text'">
      <span>{{ props.errorText }}</span>
    </template>
    <el-image :style="{ width: computedWidth, height: computedHeight, borderRadius: computedBorderRadius }"
      v-if="props.errorShow === 'image'" />
  </template>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import { getFileInfoListByIds } from '@/api/infra/file'

defineOptions({ name: 'BasicButtonViewFile' })
const emit = defineEmits(['confirm', 'delete'])

const props = defineProps({
  fileIds: {
    type: [String, Number] as PropType<number | string>,
    default: ''
  },
  width: {
    type: [Number, String],
    default: 50,
  },
  height: {
    type: [Number, String],
    default: 50,
  },
  borderRadius: propTypes.number.def(4),
  fit: {
    type: String as () => "" | "fill" | "none" | "contain" | "cover" | "scale-down",
    // required: true,
    default: 'contain'
  },
  errorShow: propTypes.string.def('text'),
  errorText: propTypes.string.def('--'),
  inTable: propTypes.bool.def(false)
})

const fileInfoList = ref([] as any[])

const getFiles = async () => {
  fileInfoList.value = []
  // 检查 fileIds 是否存在
  if (!props.fileIds) {
    return
  }
  // 验证 fileIds 是否为number或string string判断是否有，分割
  if (typeof props.fileIds === 'number') {
    getFileUrl(props.fileIds)
    return
  }

  const fileIdArray = props.fileIds.split(',')
  if (!fileIdArray.every((id) => /^\d+$/.test(id.trim()))) {
    return
  } else {
    getFileUrl(props.fileIds)
  }
}

const getFileUrl = async (fileId: any) => {
  const res = await getFileInfoListByIds({ fileIds: fileId })
  res.map((item: any) => {
    fileInfoList.value.push(item.url)
  })
}

const computedWidth = computed(() => {
  if (typeof props.width === 'number') {
    return `${props.width}px`
  } else {
    return props.width
  }
})

const computedHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`
  } else {
    return props.height
  }
})

const computedBorderRadius = computed(() => {
  if (typeof props.borderRadius === 'number') {
    return `${props.borderRadius}px`
  }
  return props.borderRadius
})

watch(
  () => props.fileIds,
  async (newValue) => {
    if (!newValue) return
    getFiles()
  },
  { immediate: true }
)
</script>
