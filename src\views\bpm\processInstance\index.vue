<template>
  <BasicPage :tabs="['我的流程']">
    <template #action>
      <el-button v-hasPermi="['bpm:process-instance:query']" @click="handleCreate(null)">发起流程</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn">
      <template #category>
        <BasicSelect
          v-model="searchForm.category"
          :options="categoryList"
          placeholder="流程分类"
          filterable
          :field-names="{ label: 'code', value: 'code' }"
        />
      </template>
    </BasicFormSearch>
    <BasicTable @register="register">
      <template #tasks="{ row }">
        <el-button type="primary" v-for="task in row.tasks" :key="task.id" link>
          <span>{{ task.name }}</span>
        </el-button>
      </template>
    </BasicTable>
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { CategoryApi } from '@/api/bpm/category'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { formatPast2 } from '@/utils/formatTime'
import { ProjectBaseInfoApi } from '@/api/pmis/project/basic'
import { useApprover } from '@/views/pmis/hooks/useApprover'

defineOptions({ name: 'BpmProcessInstanceMy' })

const message = useMessage()
const { t } = useI18n()
const router = useRouter()

const searchForm = ref<{ [key: string]: any }>({})
const categoryList = ref([])
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '详情', permCode: 'bpm:process-instance:cancel', callback: handleDetail },
    {
      label: '撤回',
      hidden: (row) => {
        return row.status === 1
      },
      permCode: 'bpm:process-instance:query',
      callback: handleCancel
    },
    {
      label: '重新发起',
      hidden: (row) => {
        return row.status !== 1
      },
      permCode: 'bpm:process-instance:cancel',
      callback: handleResubmit
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: ProcessInstanceApi.getProcessInstanceManagerPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  },
  afterFetch: async (data) => {
    const requestData = {}
    data.forEach((item: any) => {
      if (item.businessKey && item.flowCode) {
        const processKey = `${item.businessKey}_${item.flowCode}`
        item.processKey = processKey
        requestData[processKey] = item.flowCode
      }
    })
    const processFormInfoRes = await ProjectBaseInfoApi.getProcessFormInfo({ params: requestData })
    data.forEach((item: any) => {
      if (item.businessKey && item.flowCode) {
        item.formInfo = processFormInfoRes[item.processKey]
        item.approvalNo = item.formInfo?.processFormInfo?.approvalNo || '--'
      }
      item.durationInMillis = item.durationInMillis > 0 ? formatPast2(item.durationInMillis) : '-'
    })
  }
})

const searchFn = () => {
  reload()
}

/** 查看详情 */
function handleDetail(row) {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}
/** 发起流程操作 **/
const handleCreate = (id?) => {
  router.push({
    name: 'BpmProcessInstanceCreate',
    query: { processInstanceId: id }
  })
}

/** 取消按钮操作 */
async function handleCancel(row) {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByAdmin(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await reload()
}
const { handleButtonEvent, setProcessDefineKey } = useApprover({})

function handleResubmit(row: any) {
  setProcessDefineKey(row.flowCode)
  const data = { id: row.businessKey }
  handleButtonEvent('approveResubmit', data)
}
/** 初始化 **/
onMounted(async () => {
  categoryList.value = await CategoryApi.getCategorySimpleList()
})
</script>

<style></style>
