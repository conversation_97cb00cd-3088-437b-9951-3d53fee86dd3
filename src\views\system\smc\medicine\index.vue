<template>
  <BasicPage :tabs="['药品信息管理']">
    <template #action>
      <BasicButtonImport perm-code="system:medicine:import" file-name="药品信息管理"
        :template-api="MedicineApi.importTemplate" :import-api="MedicineApi.importMedicine"
        :exportError-file-api="MedicineApi.exportErrorFile" @success="handlerImportSuccess" />
      <!-- 隐藏导出按钮 -->
      <!-- <BasicButtonExport perm-code="system:medicine:export" file-name="药品信息管理" :params="{ ...searchForm }"
        :export-api="MedicineApi.exportMedicine" /> -->
      <el-button v-hasPermi="['system:medicine:create']" @click="handleSync" type="primary">主动同步</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register" />

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, editFormConfig, viewFormConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { MedicineApi } from '@/api/system/smc/medicine'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:medicine:update', callback: handleEdit },
    { label: '查看', permCode: 'system:medicine:query', callback: handleDetail },
    // { label: '删除', permCode: 'system:medicine:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: MedicineApi.getMedicinePage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}

/** 主动同步 */
const handleSync = async () => {
  try {
    await MedicineApi.syncMedicine()
    message.success('同步成功')
    reload()
  } catch (error) {
    message.error('同步失败')
  }
}

const openFormDialog = (id: number | null, type: string) => {
  // 根据操作类型选择不同的表单配置
  const isEdit = type === 'edit'
  const formConfigList = isEdit ? editFormConfig.value : viewFormConfig.value
  
  const config: any = {
    title: '',
    type,
    formConfigList,
    formData: formData.value,
    queryApi: MedicineApi.getMedicine,
    submitApi: MedicineApi.updateMedicine  // 只保留编辑功能，去掉新增
  }
  
  // 只有编辑时才添加beforeSubmit函数
  if (isEdit) {
    config.beforeSubmit = (formData: any) => {
      // 只传递id和四个可编辑字段
      return {
        id: formData.id,
        boxVolume: formData.boxVolume,
        boxWeight: formData.boxWeight,
        singleWeight: formData.singleWeight,
        accessFrequency: formData.accessFrequency
      }
    }
  }
  
  formDialogRef.value.init(id, config)
}

function handleEdit({ id }) {
  // 只允许编辑，不允许新增
  if (id) {
    openFormDialog(id, 'edit')
  }
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

// async function handleDelete({ id }) {
//   // 删除的二次确认
//   await message.delConfirm()
//   // 发起删除
//   await MedicineApi.deleteMedicine(id)
//   message.success(t('common.delSuccess'))

//   reload()
// }
</script>