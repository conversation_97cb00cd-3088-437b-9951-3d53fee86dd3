import { DICT_TYPE, getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '表单名',
      prop: 'name'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '编号', prop: 'id', minWidth: 60 },
    { label: '表单名', prop: 'name', minWidth: 160, tooltip: true },
    {
      label: '状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '备注', prop: 'remark', minWidth: 160, tooltip: true },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})


