<template>
  <BasicPage :tabs="['流程实例']">
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn">
      <template #startUserId>
        <BasicSelect
          v-model="searchForm.startUserId"
          :options="userList"
          placeholder="发起人"
          filterable
          :field-names="{ label: 'nickname', value: 'id' }"
        />
      </template>
      <template #category>
        <BasicSelect
          v-model="searchForm.category"
          :options="categoryList"
          placeholder="流程分类"
          filterable
          :field-names="{ label: 'code', value: 'code' }"
        />
      </template>
    </BasicFormSearch>
    <BasicTable @register="register">
      <template #tasks="{ row }">
        <el-button type="primary" v-for="task in row.tasks" :key="task.id" link>
          <span>{{ task.name }}</span>
        </el-button>
      </template>
    </BasicTable>
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { CategoryApi } from '@/api/bpm/category'
import * as UserApi from '@/api/system/user'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { formatPast2 } from '@/utils/formatTime'

defineOptions({ name: 'BpmProcessInstanceManager' })

const message = useMessage()
const { t } = useI18n()
const router = useRouter()

const searchForm = ref<{ [key: string]: any }>({})
const userList = ref<any[]>([]) // 用户列表
const categoryList = ref([])
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '详情', permCode: 'bpm:process-instance:query', callback: handleDetail },
    {
      label: '取消',
      permCode: 'bpm:process-instance:cancel',
      callback: handleCancel
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: ProcessInstanceApi.getProcessInstanceManagerPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  },
  afterFetch: (res) => {
    res.forEach((item: any) => {
      item.nickname = item.startUser.nickname
      item.deptName = item.startUser.deptName
      item.durationInMillis = item.durationInMillis > 0 ? formatPast2(item.durationInMillis) : '-'
    })
  }
})

const searchFn = () => {
  reload()
}

/** 查看详情 */
function handleDetail(row) {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
async function handleCancel(row) {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByAdmin(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await reload()
}
/** 初始化 **/
onMounted(async () => {
  categoryList.value = await CategoryApi.getCategorySimpleList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style></style>
