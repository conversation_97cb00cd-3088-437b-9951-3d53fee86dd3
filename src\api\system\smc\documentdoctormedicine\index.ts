import request from '@/config/axios'

// 分药医生药品关联 VO
export interface DocumentDoctorMedicineVO {
  id: number // 主键id
  acquireDate: Date // 取药（分药）日期
  recordNumber: string // 操作记录单号
  documentId: number // 单据ID
  documentNo: string // 关联单据编号
  medicineId: number // 药品ID
  nationalDrugCode: string // 国家药品标识码（唯一）
  deptId: number // 部门ID
  doctorId: number // 医生ID
  doctorConfirmTime: Date // 医生确认时间
  batchNumber: string // 批次号
  traceCode: string // 药品追溯码
  quantity: number // 数量
  productionDate: Date // 生产日期
  expiryDate: Date // 有效期截止日期
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 分药医生药品关联 API
export const DocumentDoctorMedicineApi = {
  // 查询分药医生药品关联分页
  getDocumentDoctorMedicinePage: async (params: any) => {
    return await request.get({ url: `/system/document-doctor-medicine/page`, params })
  },
  // 查询分药医生药品关联列表
  getDocumentDoctorMedicineList: async (params) => {
    return await request.get({ url: `/system/document-doctor-medicine/list`, params })
  },

  // 查询分药医生药品关联详情
  getDocumentDoctorMedicine: async (params: any) => {
    return await request.get({ url: `/system/document-doctor-medicine/get`, params })
  },

  // 新增分药医生药品关联
  createDocumentDoctorMedicine: async (data: DocumentDoctorMedicineVO) => {
    return await request.post({ url: `/system/document-doctor-medicine/create`, data })
  },

  // 修改分药医生药品关联
  updateDocumentDoctorMedicine: async (data: DocumentDoctorMedicineVO) => {
    return await request.put({ url: `/system/document-doctor-medicine/update`, data })
  },

  // 删除分药医生药品关联
  deleteDocumentDoctorMedicine: async (id: number) => {
    return await request.delete({ url: `/system/document-doctor-medicine/delete?id=` + id })
  },

  // 导出分药医生药品关联 Excel
  exportDocumentDoctorMedicine: async (params) => {
    return await request.download({ url: `/system/document-doctor-medicine/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/document-doctor-medicine/get-import-template` })
  },

  // 导入分药医生药品关联 Excel
  importDocumentDoctorMedicine: async (formData) => {
    return await request.upload({ url: `/system/document-doctor-medicine/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/document-doctor-medicine/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}