import { JSEncrypt } from 'jsencrypt'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey =
  "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCnRrPXHjrUztDgYW4B4KCVMBbX\n" +
  "WKBtT6wiZ4+DuB+HijIgQHIeiL7NW5QwRFN7g1PZAlAwJfSbv8Lgul+keF8+BGGY\n" +
  "JK6q9ahs4eoV1c5T7Yg6al0LsdXtpma8AZJV7V0XxQndV0T8adKT6ieFM06eIOul\n" +
  "P4HbPK/yZxXYi73ycwIDAQAB"

const privateKey =
  "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKdGs9ceOtTO0OBh\n" +
  "bgHgoJUwFtdYoG1PrCJnj4O4H4eKMiBAch6Ivs1blDBEU3uDU9kCUDAl9Ju/wuC6\n" +
  "X6R4Xz4EYZgkrqr1qGzh6hXVzlPtiDpqXQux1e2mZrwBklXtXRfFCd1XRPxp0pPq\n" +
  "J4UzTp4g66U/gds8r/JnFdiLvfJzAgMBAAECgYABevq0eKwEnh/zHGmg0qAZMefB\n" +
  "2CLzVLUtSMVYYRTGWk6/QlLAOm2V262gD7q9aRgg34heJLfZeQT8gCnAhuAw5KcH\n" +
  "cKx9d5V2ht0hPD8+ugfRSNLFRRddh1t6aLNYdMFm7Z3+mLx5s+RXec/3LS/OdXUF\n" +
  "8iqfbL7XTL7uHopnIQJBANRw0UOW1hvA/Mb/r3PJ0nf8RdYBqe0B0OFcG+iOBYbo\n" +
  "383bCA3LhPcyH5RSqf4m6xeTIJGx8KUVEKsIBY7/Ji8CQQDJkynhH7bOBIUlqQgz\n" +
  "5vHjpr2QzTd1GZOfhW6VmBtj3koNujbzX6xASjkfWNVnCdeN/PmPjB/WKd5iyzxE\n" +
  "5qr9AkAueH9OSbCMYarp3QSET6G6nXSpW7PQJJWMtplkRazv1gEfBsq8OcjcShqe\n" +
  "V3bYIatLfOOi+0mo0vLSdu7AWRABAkEAtO79pm9bHS19fkJtdHkWImumuHUcFGYy\n" +
  "1ftlOa3g2BHNkujVLjtwV/zHrVWFqAlzjKXINItogEnwrekTXh1W2QJAFkAHIJR4\n" +
  "/o6zqMl2r1WYgeIXSpRlYr//BaaFfaFX4UgTbF+bYehgD6dYAyeJFNw15wLnnztk\n" +
  "bbog1tzy1f+3Qw=="

// 加密
export const encrypt = (txt: string) => {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export const decrypt = (txt: string) => {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}
