<template>
  <div class="pmis-page-sub-content">
    <el-row class="header" :class="{ border: border }" justify="space-between" align="middle" :style="`padding:${titlePaddingS}`">
      <ElButton v-if="back" text class="back-btn" @click="handleBack">
        <Icon icon="ep:close" class="mr-2px" />
        返回
      </ElButton>
      <div class="line"></div>
      <div class="title">{{ title }}</div>
      <div><slot name="action"></slot></div>
    </el-row>
    <div><slot name="tip"></slot></div>
    <div class="content">
      <slot default></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import 'element-plus/theme-chalk/display.css'
import { useTagsViewStore } from '@/store/modules/tagsView'

defineOptions({ name: 'BasicPage' })

const emit = defineEmits(['back', 'confirm', 'cancel'])

const props = defineProps({
  back: propTypes.bool.def(true),
  title: propTypes.string.def('详情'),
  border: propTypes.bool.def(true),
  titlePadding: propTypes.array.def([0, 0, 10])
})

const tagsViewStore = useTagsViewStore()
const route = useRoute()

const titlePaddingS = computed(() => {
  let padding = props.titlePadding
  padding.forEach((item: any, index: any) => {
    padding[index] = `${item}px`
  })
  return padding.join(' ')
})

const handleBack = () => {
  tagsViewStore.delView(route)
  tagsViewStore.toLastView()
}
</script>

<style lang="scss" scoped>
.pmis-page-sub-content {
  position: relative;
  background-color: white;
  padding: 14px;
}
.back-btn {
  position: absolute;
  width: 60px;
  margin-left: -6px;
}
.line {
  position: absolute;
  width: 1px;
  height: 12px;
  background-color: #e8e8e8;
  margin-left: 56px;
}
.title {
  color: #191919;
  font-size: 16px;
  font-weight: 500;
  margin-left: 64px;
}
.header {
  margin-bottom: 14px;
  width: calc(100% - 28px);
}
.border {
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}
.content {
  width: 80%;
  margin: 0 auto;
}
</style>
