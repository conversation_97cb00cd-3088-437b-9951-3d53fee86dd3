<template>
  <el-button
    v-if="hasPermi"
    :loading="btnLoading"
    :disabled="disabled"
    :size="size"
    @click="handleExport"
  >
    <span>{{ title }}</span>
  </el-button>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import download from '@/utils/download'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

defineOptions({ name: 'BasicButtonExport' })

const { wsCache } = useCache()

const props = defineProps({
  title: propTypes.string.def('导出'),
  permCode: propTypes.string.def(''),
  fileName: propTypes.string.def(''),
  size: propTypes.string.def('default'),
  method: propTypes.string.def('excel'),
  disabled: propTypes.bool.def(false),
  params: propTypes.object.def({}), // 额外参数
  exportApi: {
    type: Function
  }
})

const hasPermi = ref(true)
const btnLoading = ref(false)

const handleExport = async () => {
  if (!props.exportApi) return
  try {
    btnLoading.value = true
    const res = await props.exportApi(props.params)
    if (props.method == 'excel') {
      download.excel(res, `${props.fileName}.xlsx`)
    } else if (props.method == 'word') {
      download.word(res, `${props.fileName}.docx`)
    }
  } finally {
    btnLoading.value = false
  }
}

onMounted(() => {
  const permissions = wsCache.get(CACHE_KEY.USER).permissions
  const hasPermissions = permissions.some(
    (permission: string) => permission.includes(props.permCode) || !props.permCode
  )
  hasPermi.value = hasPermissions
})
</script>

<style scoped></style>
