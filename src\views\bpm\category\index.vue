<template>
  <BasicPage :tabs="['流程分类']">
    <template #action>
      <el-button v-hasPermi="['bpm:category:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { CategoryApi } from '@/api/bpm/category'

defineOptions({ name: 'BpmCategory' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    { label: '编辑', permCode: 'bpm:category:update', callback: handleEdit },
    {
      label: '删除',
      permCode: 'bpm:category:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: CategoryApi.getCategoryPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: CategoryApi.getCategory,
    submitApi: id ? CategoryApi.updateCategory : CategoryApi.createCategory,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await CategoryApi.deleteCategory(id)
  message.success(t('common.delSuccess'))
  reload()
}
</script>

<style></style>
