<template>
  <div class="training-form-dialog">
    <el-dialog
      v-bind="$attrs"
      v-model="innerVisible"
      :title="title"
      append-to-body
      draggable
      :width="width"
      :fullscreen="fullscreen"
      :close-on-click-modal="!showFooter"
      class="training-dialog"
      @close="handleClose"
    >
      <div class="fullscreen">
        <Icon :icon="fullscreen ? 'radix-icons:exit-full-screen' : 'radix-icons:enter-full-screen'" @click="fullscreen = !fullscreen" />
      </div>
      <div class="content">
        <slot name="content"></slot>
      </div>
      <template v-if="showFooter" #footer>
        <slot name="action">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
          <slot name="btns"></slot>
        </slot>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup name="BasicFormDialog">
import { propTypes } from '@/utils/propTypes'

const emit = defineEmits(['close', 'submit', 'update:show'])

const props = defineProps({
  show: propTypes.bool.def(false),
  title: propTypes.string.def('标题'),
  showFooter: propTypes.bool.def(true),
  width: propTypes.string.def('50%'),
})

const innerVisible = ref(false)
const fullscreen = ref(false)

const handleConfirm = async () => {
  emit('submit')
}

const handleClose = () => {
  emit('update:show', false)
}

watch(
  () => props.show,
  (val) => {
    if (!val) {
      emit('close')
    }
    innerVisible.value = val
  },
  { immediate: true }
)
</script>

<style lang="scss">
.training-dialog {
  padding: 0;
  .el-dialog__header {
    border-bottom: 0.02em solid rgba(0, 0, 0, 0.06);
    text-align: left;
    margin-right: 0;
    padding: 14px;
    .el-dialog__headerbtn {
      width: 16px !important;
      height: 16px !important;
      top: 20px !important;
      right: 14px !important;
      transition: all 0.25s;
      &:hover {
        transform: scale(1.4) !important;
      }
    }
    .el-dialog__title {
      color: #191919;
      font-size: 18px;
      font-weight: 500;
    }
  }
  .el-dialog__body {
    padding: 0;
    .content {
      overflow-y: auto;
      min-height: 200px;
      max-height: 500px;
      padding: 10px 14px 14px;
    }

    .fullscreen {
      position: absolute;
      top: 13px;
      right: 40px;
      cursor: pointer;
      width: 30px;
      height: 30px;
      transition: all 0.25s;
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--el-color-info);
      &:hover {
        transform: scale(1.4) !important;
        color: var(--el-color-primary);
      }
    }
  }
  .el-dialog__footer {
    padding: 10px 14px;
    border-top: 0.02em solid rgba(0, 0, 0, 0.06);
    .el-button {
      color: #1d2129;
      background-color: white;
    }
    .el-button--primary {
      color: white;
      background-color: var(--el-color-primary);
    }
  }
}

.is-fullscreen {
  .el-dialog__body {
    overflow-y: auto;
    padding: 0;
    .content {
      overflow-y: auto;
      max-height: calc(100vh - 145px);
      padding: 20px;
    }
  }
  .el-dialog__footer {
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: white;
    width: 100%;
    height: 50px;
    border-top: 0.02em solid rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    z-index: 2011;
  }
}
</style>
