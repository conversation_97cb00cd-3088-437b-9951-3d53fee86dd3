import { DICT_TYPE, getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '组名',
      prop: 'name'
    },
    {
      component: 'select',
      label: '状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '创建时间开始',
      endPlaceholder: '创建时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '编号', prop: 'id', minWidth: 60 },
    { label: '组名', prop: 'name', minWidth: 140, tooltip: true },
    { label: '描述', prop: 'description', minWidth: 140, tooltip: true },
    { label: '成员', slot: 'users', minWidth: 180, tooltip: true },
    {
      label: '分类状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '组名',
        prop: 'name',
        rules: [{ required: true, message: '组名不能为空', trigger: 'blur' }]
      },
      {
        component: 'textarea',
        label: '描述',
        prop: 'description'
      },
      {
        label: '成员',
        slot: 'userIds',
        rules: [{ required: true, message: '成员不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      }
    ]
  }
])
