import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
import { DeptApi } from "@/api/system/dept/deptOption";
import { MedicineApi } from "@/api/system/smc/medicine";

export const formSearchConfig = {
  itemList: [
    {
      component: 'select',
      label: '药品',
      prop: 'medicineId',
      placeholder: '请选择药品',
      params: {},
      filterable: true,
      fieldNames: { label: 'medicineName', value: 'id', id: 'id', parentId: 'parentId' },
      api: MedicineApi.getMedicineList,
    },
    // {
    //   component: 'select',
    //   label: '是否已禁用',
    //   prop: 'status',
    //   placeholder: '请选择是否已禁用',
    //   params: {},
    //   filterable: true,
    //   // todo 请完善字典类型 到 DICT_TYPE中
    //   options: getIntDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)
    // },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '部门',
      prop: 'deptIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药品名称',
      prop: 'medicineIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '规格',
      prop: 'specification',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单位',
      prop: 'unit',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '数量',
      prop: 'baseQuantity',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '预计使用天数',
      prop: 'estimatedDays',
      tooltip: true,
      minWidth: 100
    },
    // {
    //   label: '是否已禁用',
    //   prop: 'status',
    //   minWidth: 100,
    //   filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    // },
    // {
    //   label: '排序',
    //   prop: 'sort',
    //   tooltip: true,
    //   minWidth: 100
    // },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    // {
    //   label: '备注',
    //   prop: 'remark',
    //   tooltip: true,
    //   minWidth: 100
    // },

  ]
})

export const formConfig = ref([
  {
    title: '药品基数配置',
    items: [
      {
        component: 'treeSelect',
        label: '部门',
        prop: 'deptId',
        placeholder: '请选择部门',
        filterable: true,
        params: {},
        propName: 'deptIdName',
        fieldNames: { id: 'id', parentId: 'parentId', label: 'name', value: 'id' },
        api: DeptApi.getDeptList,
        rules: [{ required: true, message: '部门不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '药品',
        prop: 'medicineId',
        placeholder: '请选择药品',
        params: {},
        filterable: true,
        propName: 'medicineIdName',
        fieldNames: { label: 'medicineName', value: 'id', id: 'id', parentId: 'parentId' },
        api: MedicineApi.getMedicineList,
        rules: [{ required: true, message: '药品不能为空', trigger: 'change' }]
      },

      {
        component: 'input',
        label: '规格',
        prop: 'specification',
        placeholder: '1mg',
        disabled: true,
        defaultValue: '1mg',
        // rules: [{ required: true, message: '规格不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '单位',
        prop: 'unit',
        placeholder: '支',
        disabled: true,
        defaultValue: '支',
        // rules: [{ required: true, message: '单位不能为空', trigger: 'blur' }]
      },
      {
        component: 'number',
        label: '数量（支）',
        prop: 'baseQuantity',
        max: 9999999999,
        placeholder: '请输入数量',
        rules: [{ required: true, message: '数量不能为空', trigger: 'blur' }]
      },
      {
        component: 'number',
        label: '预计使用天数',
        prop: 'estimatedDays',
        max: 9999999999,
        placeholder: '请输入预计使用天数',
        rules: [{ required: true, message: '预计使用天数不能为空', trigger: 'blur' }]
      },
    ]
  }
])
