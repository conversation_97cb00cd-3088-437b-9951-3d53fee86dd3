<template>
  <el-row :gutter="20">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <DeptTree @node-click="handleDeptNodeClick" />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <BasicPage :tabs="['用户管理']">
        <template #action>
          <BasicButtonImport
            perm-code="system:user:import"
            file-name="用户信息"
            :template-api="UserApi.importUserTemplate"
            :import-api="UserApi.importUser"
            :exportError-file-api="UserApi.exportErrorFile"
            @success="() => reload()"
          />
          <BasicButtonExport
            perm-code="system:user:export"
            file-name="用户信息"
            :params="{ ...searchForm }"
            :export-api="UserApi.exportUser"
          />
          <el-button v-hasPermi="['system:user:create']" @click="handleEdit">新增</el-button>
        </template>
        <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
        <BasicTable @register="register" />
        <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()">
          <template #deptId>
            <el-tree-select
              v-model="formData.deptId"
              :data="deptList"
              :props="defaultProps"
              check-strictly
              node-key="id"
              placeholder="请选择归属部门"
            />
          </template>
          <template #postIds>
            <BasicSelect
              v-model="formData.postIds"
              v-model:label="formData.post"
              :options="postList"
              multiple
              :field-names="{ label: 'name', value: 'id' }"
              filterable
            />
          </template>
          <template #roleIds>
            <BasicSelect
              v-model="formData.roleIds"
              v-model:label="formData.role"
              :options="roleList"
              multiple
              :field-names="{ label: 'name', value: 'id' }"
              filterable
            />
          </template>
        </BasicFormDrawer>
      </BasicPage>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, filterFormConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as DeptApi from '@/api/system/dept'
import * as PostApi from '@/api/system/post'
import * as RoleApi from '@/api/system/role'
import * as PermissionApi from '@/api/system/permission'
import * as UserApi from '@/api/system/user'
import { defaultProps, handleTree } from '@/utils/tree'
import DeptTree from './DeptTree.vue'

defineOptions({ name: 'SystemDept' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const postList: any = ref([])
const deptList = ref<Tree[]>([]) // 树形结构
const roleList: any = ref([])

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,

  buttons: [
    { label: '编辑', permCode: 'system:user:update', callback: handleEdit },
    {
      label: '删除',
      permCode: 'system:user:delete',
      color: 'red',
      callback: handleDelete
    },
    {
      label: '重置密码',
      permCode: 'system:user:update-password',
      callback: handlePassword
    },
    {
      label: '分配角色',
      permCode: 'system:permission:assign-user-role',
      callback: handleEdit
    }
  ]
}
const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: UserApi.getUserPage,
  columns: totalColumns,
  rowKey: 'id',
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (data: any) => {
    const list = handleTree(data)
    return list
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = async (id: number | null, type: string) => {
  if (type == 'add') {
    formData.value.status = 0
  }
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: filterFormConfig(type),
    formData: formData.value,
    queryApi: UserApi.getUser,
    submitApi: async (data: any) => {
      if (id) {
        // 编辑用户：先更新用户信息，再分配角色
        const userResult = await UserApi.updateUser(data)
        if (data.roleIds && data.roleIds.length > 0) {
          await PermissionApi.assignUserRole({
            userId: id,
            roleIds: data.roleIds
          })
        }
        return userResult
      } else {
        // 新增用户：先创建用户，再分配角色
        const userResult = await UserApi.createUser(data)
        if (data.roleIds && data.roleIds.length > 0) {
          await PermissionApi.assignUserRole({
            userId: userResult,
            roleIds: data.roleIds
          })
        }
        return userResult
      }
    },
    afterFetch: async (res: any) => {
      if (res.sex === 0) {
        res.sex = null
      }
      // 如果是编辑用户，获取用户的角色信息
      if (id && type === 'edit') {
        try {
          res.roleIds = await PermissionApi.getUserRoleList(id)
        } catch (error) {
          console.error('获取用户角色失败:', error)
          res.roleIds = []
        }
      }
      formData.value = res
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

/** 处理部门被点击 */
const handleDeptNodeClick = (row) => {
  searchForm.value.deptId = row.id
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await UserApi.deleteUser(id)
  message.success(t('common.delSuccess'))
  reload()
}

/** 重置密码 */
async function handlePassword(row: any) {
  try {
    // 重置的二次确认
    const result = await message.prompt('请输入"' + row.username + '"的新密码', t('common.reminder'))
    const password = result.value

    // 密码正则校验
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?.&#^~])[A-Za-z\d@$!%*?.&#^~]{8,20}$/
    if (!passwordRegex.test(password)) {
      message.error('密码必须包含数字、字母和特殊字符(@$!%*?&#^~)，长度8-20位')
      // 重新打开密码输入框
      return handlePassword(row)
    }
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 分配角色 */
const theType = ref('')
async function handleRole(row: UserApi.UserVO) {
  theType.value = 'role'
  formData.value.roleIds = await PermissionApi.getUserRoleList(row.id)
  formData.value.userId = row.id
  formData.value.nickname = row.nickname
  formData.value.username = row.username
  openFormDialog(null, 'edit')
}
onMounted(async () => {
  deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  postList.value = await PostApi.getSimplePostList()
  roleList.value = await RoleApi.getSimpleRoleList()
})
</script>

<style></style>
