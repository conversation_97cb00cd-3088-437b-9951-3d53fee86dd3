import request from '@/config/axios'

// 项目基础信息 VO
export interface ProjectBaseInfoVO {
  id: number // 主键ID
  deptId: number // 部门ID
  projectNature: string // 项目性质
  projectName: string // 项目名称
  projectNo: string // 项目编号
  projectCategoryId: string // 项目类型
  star: boolean // 是否星标项目（0否 1是）
  needTenderDocuments: boolean // 是否需要招标文件（0否 1是）
  controlPriceAudit: boolean // 是否控制价审核（0否 1是）
  mainConstructionContent: string // 主要建设内容
  amountLimit: number // 金额限制
  needExtractRegistration: boolean // 是否需要抽签报名（0否 1是）
  constructionYears: number // 建设年限
  projectApprovalNumber: string // 立项文号
  approvalTime: Date // 立项时间
  fundSource: string // 资金来源
  budgetAmount: number // 预算金额（万元）
  totalAmount: number // 立项(合同）金额（万元）
  buildAmount: number // 施工金额（万元）
  supervisionAmount: number // 监理金额（万元）
  scheduleRate: number // 项目进度
  attachmentIds: string // 附件IDs
  handoverTime: Date // 移交时间
  handoverUnit: string // 接收单位
  handoverDesc: string // 移交说明
  handoverAttachmentIds: string // 移交附件
  propertyRightTime: Date // 产权办理登记时间
  recordRegistrationTime: Date // 备案登记时间
  planStartTime: Date // 计划开工时间
  planCompletionTime: Date // 计划竣工时间
  actualStartTime: Date // 开工时间
  actualCompletionTime: Date // 竣工时间
  remark: string // 备注
  amountDiffReason: string // 差额原因
  status: number // 项目状态
}

// 项目基础信息 API
export const ProjectBaseInfoApi = {

  // 通过业务id和流程模型key查询对应业务表单信息 ，各系统自行实现
  getProcessFormInfo: async (data: any) => {
    return await request.post({ url: '/pmis/bmp/process/formInfo', data })
  },

}
