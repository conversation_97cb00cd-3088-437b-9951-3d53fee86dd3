<template>
  <BasicPage :tabs="['空瓶回收记录管理']">
    <template #action>
      <BasicButtonImport
              perm-code="system:empty-bottle-record:import"
              file-name="空瓶回收记录管理"
              :template-api="EmptyBottleRecordApi.importTemplate"
              :import-api="EmptyBottleRecordApi.importEmptyBottleRecord"
              :exportError-file-api="EmptyBottleRecordApi.exportErrorFile"
              @success="handlerImportSuccess"
      />
      <BasicButtonExport
              perm-code="system:empty-bottle-record:export"
              file-name="空瓶回收记录管理"
              :params="{ ...searchForm }"
              :export-api="EmptyBottleRecordApi.exportEmptyBottleRecord"
      />
      <el-button v-hasPermi="['system:empty-bottle-record:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register"/>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { EmptyBottleRecordApi} from '@/api/system/smc/emptybottlerecord'

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 140,
  buttons: [
    { label: '编辑', permCode: 'system:empty-bottle-record:update', callback: handleEdit },
    { label: '查看', permCode: 'system:empty-bottle-record:query', callback: handleDetail },
    { label: '删除', permCode: 'system:empty-bottle-record:delete', color: 'red', callback: handleDelete }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({

  api: EmptyBottleRecordApi.getEmptyBottleRecordPage,
  columns: totalColumns,

  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: EmptyBottleRecordApi.getEmptyBottleRecord,
    submitApi: id ? EmptyBottleRecordApi.updateEmptyBottleRecord : EmptyBottleRecordApi.createEmptyBottleRecord
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function handleDetail({ id }) {
  openFormDialog(id, 'detail')
}

async function handleDelete({ id }) {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await EmptyBottleRecordApi.deleteEmptyBottleRecord(id)
  message.success(t('common.delSuccess'))

  reload()
}
</script>