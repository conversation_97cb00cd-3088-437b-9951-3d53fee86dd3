import { ComputedRef, Ref, Watch<PERSON>topHandle } from 'vue'

const getDynamicProps = <T, U>(props: T): Partial<U> => {
  const ret: Record<string, any> = {}

  // @ts-ignore
  Object.keys(props).map((key) => {
    ret[key] = unref((props as Record<string, any>)[key])
  })

  return ret as Partial<U>
}

export interface TableProps {
  api?: Promise<any> | Function | null
  data?: any[]
  columns: ComputedRef<any[]> | any[] | Ref<any[]>
  pagination?:
  | {
    pageSize?: number
    pageSizes?: number[]
    pageNum?: number
  }
  | boolean
  immediate?: boolean
  rowKey?: String
  tooltip?: boolean
  highlightCurrentRow?: boolean
  columnsSetting?: boolean
  multiple?: boolean
  showSummary?: boolean
  border?: boolean
  beforeFetch?: (params: any) => any
  afterFetch?: (res: any) => any
  selectionChange?: (isSelected?: boolean, selectedList?: any[], selectedListIds?: string[] | number[]) => void
  currentChange?: (currentRow: any) => void
  summaryMethod?: (row: any[]) => void
}
export interface MethodType {
  reload: (opt?: any) => Promise<void>
  setApi: (api: Promise<any> | Function) => void
  setProps: (props: Partial<any>) => void
  getProps: () => any
  setColumns: (info: any) => void
  getColumns: () => any
  getSelectionRows: () => any
  getSelectionIds: () => any
  setSelectionRow: (info: { [key: string]: any }[]) => void
  setCurrentRow: (info?: any) => void
  getCurrentRow: () => any
  setSelectedRows: (info: any) => void
  updatePagination: (info: any) => void
  setData: (info: any) => void
}
export function useTable(tableProps: TableProps): [(instance: any) => void, MethodType] {
  const tableRef = ref<Nullable<any>>(null)
  let stopWatch: WatchStopHandle
  function register(instance: any) {
    tableRef.value = instance
    tableProps && instance.setProps(getDynamicProps(tableProps))

    stopWatch?.()

    stopWatch = watch(
      () => tableProps,
      () => {
        tableProps && instance.setProps(getDynamicProps(tableProps))
      },
      {
        immediate: true,
        deep: true
      }
    )
  }

  function getTableInstance() {
    const table = unref(tableRef)
    if (!table) {
      console.error('表格实列不存在！')
    }
    return table
  }

  const methods: MethodType = {
    reload: async (opt?: any) => {
      return await getTableInstance()?.reload(opt)
    },
    setApi: (api: Promise<any> | Function) => {
      getTableInstance()?.setApi(api)
    },
    setProps: (props: Partial<any>) => {
      getTableInstance()?.setProps(props)
    },
    getProps: () => {
      return getTableInstance()?.getProps
    },
    setColumns: (info: any) => {
      getTableInstance()?.setColumns(info)
    },
    getColumns: () => {
      return getTableInstance()?.getColumns()
    },
    getSelectionRows: () => {
      return getTableInstance()?.selectedList
    },
    getSelectionIds: () => {
      return getTableInstance()?.selectedListIds
    },
    setSelectionRow: (info: { [key: string]: any }[]) => {
      getTableInstance()?.selectionChange(info)
    },
    setCurrentRow: (info: any = null) => {
      getTableInstance()?.setCurrentRow(info)
    },
    getCurrentRow: () => {
      return getTableInstance()?.currentRow
    },
    setSelectedRows: (rows: any) => {
      getTableInstance()?.setSelectedRows(rows)
    },
    updatePagination: (pagination: any) => {
      getTableInstance()?.updatePagination(pagination)
    },
    setData: (info: any = null) => {
      getTableInstance()?.setData(info)
    }
  }

  return [register, methods]
}
