import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
  import {CabinetApi} from "@/api/system/smc/cabinet";

export const formSearchConfig = {
  itemList: [
    {
      component: 'select',
      label: '药柜ID',
      prop: 'cabinetId',
      placeholder: '请选择药柜ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: CabinetApi.getCabinetList,
    },
    {
      component: 'input',
      label: '抽屉ID',
      prop: 'drawerId',
      placeholder: '请输入抽屉ID'
    },
    {
      component: 'input',
      label: '格口ID',
      prop: 'slotId',
      placeholder: '请输入格口ID'
    },
    {
      component: 'input',
      label: '抽屉号',
      prop: 'drawerName',
      placeholder: '请输入抽屉号'
    },
    {
      component: 'input',
      label: '格口号',
      prop: 'slotName',
      placeholder: '请输入格口号'
    },
    {
      component: 'select',
      label: '操作类型:inbound/outbound',
      prop: 'operationType',
      placeholder: '请选择操作类型:inbound/outbound',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_OPERATION_TYPE)
    },
    {
      component: 'select',
      label: '业务类型:补药入柜/取药出柜/分药出柜等',
      prop: 'businessType',
      placeholder: '请选择业务类型:补药入柜/取药出柜/分药出柜等',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_BUSINESS_TYPE)
    },
    {
      component: 'select',
      label: '锁操作:open/close',
      prop: 'lockAction',
      placeholder: '请选择锁操作:open/close',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_OPERATION_TYPE)
    },
    {
      component: 'datePickerRange',
      label: '锁操作时间',
      prop: 'operateTime',
      startPlaceholder: '锁操作时间开始日期',
      endPlaceholder: '锁操作时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'number',
      label: '操作时长(秒)',
      prop: 'operationDuration',
      placeholder: '请输入操作时长(秒)'
    },
    {
      component: 'input',
      label: '操作人员',
      prop: 'operatorName',
      placeholder: '请输入操作人员'
    },
    {
      component: 'input',
      label: '关联业务记录ID',
      prop: 'relatedRecordId',
      placeholder: '请输入关联业务记录ID'
    },
    {
      component: 'input',
      label: '操作视频链接',
      prop: 'videoUrl',
      placeholder: '请输入操作视频链接'
    },
    {
      component: 'select',
      label: '锁状态:opened/closed/abnormal',
      prop: 'lockStatus',
      placeholder: '请选择锁状态:opened/closed/abnormal',
      params: {},
      filterable: true,
              // todo 请完善字典类型 到 DICT_TYPE中
      options: getStrDictOptions(DICT_TYPE.SMC_LOCK_STATUS)
    },
    {
      component: 'number',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'number',
      label: '排序',
      prop: 'sort',
      placeholder: '请输入排序'
    },
    {
      component: 'input',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '药柜ID',
      prop: 'cabinetIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '抽屉ID',
      prop: 'drawerId',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '格口ID',
      prop: 'slotId',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '抽屉号',
      prop: 'drawerName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '格口号',
      prop: 'slotName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作类型:inbound/outbound',
      prop: 'operationType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_OPERATION_TYPE, row.operationType)
    },
    {
      label: '业务类型:补药入柜/取药出柜/分药出柜等',
      prop: 'businessType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_BUSINESS_TYPE, row.businessType)
    },
    {
      label: '锁操作:open/close',
      prop: 'lockAction',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_OPERATION_TYPE, row.lockAction)
    },
    {
      label: '锁操作时间',
      prop: 'operateTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: '操作时长(秒)',
      prop: 'operationDuration',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作人员',
      prop: 'operatorName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '关联业务记录ID',
      prop: 'relatedRecordId',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '操作视频链接',
      prop: 'videoUrl',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '锁状态:opened/closed/abnormal',
      prop: 'lockStatus',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_LOCK_STATUS, row.lockStatus)
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '开关锁记录',
    items: [
      {
        component: 'select',
        label: '药柜ID',
        prop: 'cabinetId',
        placeholder: '请选择药柜ID',
        params: {},
        filterable: true,
        propName: 'cabinetIdName',
        fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
        api: CabinetApi.getCabinetList,
        rules: [{ required: true, message: '药柜ID不能为空', trigger: 'change' }]
      },
      {
        component: 'input',
        label: '抽屉ID',
        prop: 'drawerId',
        placeholder: '请输入抽屉ID',
        rules: [
              { required: true, message: '抽屉ID不能为空', trigger: 'blur' },
              { max: 19, message: '长度不能超过19个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '格口ID',
        prop: 'slotId',
        placeholder: '请输入格口ID',
        rules: [
              { max: 19, message: '长度不能超过19个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '抽屉号',
        prop: 'drawerName',
        placeholder: '请输入抽屉号',
        rules: [
              { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '格口号',
        prop: 'slotName',
        placeholder: '请输入格口号',
        rules: [
              { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '操作类型:inbound/outbound',
        prop: 'operationType',
        placeholder: '请选择操作类型:inbound/outbound',
        params: {},
        filterable: true,
        options: getStrDictOptions(DICT_TYPE.SMC_OPERATION_TYPE),
        rules: [{ required: true, message: '操作类型:inbound/outbound不能为空', trigger: 'change' }]
      },
      {
        component: 'select',
        label: '业务类型:补药入柜/取药出柜/分药出柜等',
        prop: 'businessType',
        placeholder: '请选择业务类型:补药入柜/取药出柜/分药出柜等',
        params: {},
        filterable: true,
        options: getStrDictOptions(DICT_TYPE.SMC_BUSINESS_TYPE),
      },
      {
        component: 'select',
        label: '锁操作:open/close',
        prop: 'lockAction',
        placeholder: '请选择锁操作:open/close',
        params: {},
        filterable: true,
        options: getStrDictOptions(DICT_TYPE.SMC_OPERATION_TYPE),
        rules: [{ required: true, message: '锁操作:open/close不能为空', trigger: 'change' }]
      },
      {
        component: 'datePicker',
        label: '锁操作时间',
        prop: 'operateTime',
        placeholder: '选择锁操作时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'number',
        label: '操作时长(秒)',
        prop: 'operationDuration',
        max: 9999999999,
        placeholder: '请输入操作时长(秒)',
      },
      {
        component: 'input',
        label: '操作人员',
        prop: 'operatorName',
        placeholder: '请输入操作人员',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '关联业务记录ID',
        prop: 'relatedRecordId',
        placeholder: '请输入关联业务记录ID',
        rules: [
              { max: 19, message: '长度不能超过19个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '操作视频链接',
        prop: 'videoUrl',
        placeholder: '请输入操作视频链接',
        rules: [
              { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
        ]
      },
      //todo  单选框 radio     label: '锁状态:opened/closed/abnormal',      prop: 'lockStatus',
      {
        component: 'select',
        label: '锁状态:opened/closed/abnormal',
        prop: 'lockStatus',
        placeholder: '请选择锁状态:opened/closed/abnormal',
          options: getStrDictOptions(DICT_TYPE.SMC_LOCK_STATUS),
      },
      {
        component: 'number',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        max: 999,
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
    ]
  }
])
