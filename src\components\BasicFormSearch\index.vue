<template>
  <el-row :gutter="10" ref="baseFormSearch" class="basic-form-search" :class="classKey">
    <div class="items" style="width: calc(100% - 185px)">
      <el-row :gutter="10">
        <el-col v-for="(item, idx) in itemList" :key="'form-item-' + idx" :xs="20" :sm="10" :md="8" :lg="6" :xl="4" class="mb-14px">
          <slot v-if="item.slot" :name="item.slot"></slot>
          <el-input
            v-if="item.component === 'input'"
            v-model="formData[item.prop]"
            id="input"
            :disabled="item.disabled"
            :readonly="item.readonly"
            :placeholder="item.placeholder || item.label"
            :maxlength="item.maxlength || 200"
            clearable
            style="width: 100%"
            ref="input"
            @focus="handleFocus($event, item)"
            @keydown.enter="searchClicked"
            @clear="searchClicked"
          />
          <template v-if="item.component === 'select'">
            <el-select-v2
              v-if="!item.onChange"
              v-model="formData[item.prop]"
              id="select"
              :options="item.options || []"
              :disabled="item.disabled"
              :placeholder="item.placeholder || item.label"
              clearable
              :filterable="item.filterable?item.filterable:true"
              style="width: 100%"
              ref="select"
              @focus="handleFocus($event, item)"
            />
            <el-select-v2
              v-else-if="item.onChange"
              v-model="formData[item.prop]"
              id="select"
              :options="item.options || []"
              :disabled="item.disabled"
              :placeholder="item.placeholder || item.label"
              clearable
              :filterable="item.filterable?item.filterable:true"
              style="width: 100%"
              @focus="handleFocus($event, item)"
              @change="item.onChange"
            />
          </template>
          <!-- tree  -->
          <el-tree-select
            v-else-if="item.component === 'treeSelect'"
            id="treeSelect"
            v-model="formData[item.prop]"
            :data="item.options || []"
            :disabled="item.disabled"
            :placeholder="item.placeholder || item.label"
            clearable
            :filterable="item.filterable?item.filterable:true"
            style="width: 100%"
            check-strictly
            @focus="handleFocus($event, item)"
            @change="item.onChange"
          />
          <!-- 年月日选择器 -->
          <el-date-picker
            v-else-if="item.component === 'datePicker'"
            v-model="formData[item.prop]"
            id="datePicker"
            :type="item.type"
            :value-format="item.dateFormate || 'YYYY-MM-DD'"
            :disabled="item.disabled"
            :placeholder="item.label"
            clearable
            style="width: 100%"
            @focus="handleFocus($event, item)"
            @change="item.onChange"
          />
          <el-date-picker
            v-else-if="item.component === 'datePickerRange'"
            v-model="formData[item.prop]"
            id="datePickerRange"
            :value-format="item.dateFormate"
            :type="item.type || 'daterange'"
            :format="item.format"
            :disabled="item.disabled"
            :placeholder="item.label"
            range-separator="-"
            :start-placeholder="item.startPlaceholder || '开始日期'"
            :end-placeholder="item.endPlaceholder || '结束日期'"
            :default-time="item.type === 'daterange' ? [new Date('1 00:00:00'), new Date('1 23:59:59')] : ''"
            clearable
            style="width: inherit"
            @focus="handleFocus($event, item)"
            @change="item.onChange"
          />

        </el-col>
      </el-row>
    </div>
    <div class="sub-item-btn" style="width: 185px">
      <el-button v-hasPermi="[permCode]" size="small" @click="resetClicked">重置</el-button>
      <el-button v-hasPermi="[permCode]" size="small" type="primary" @click="searchClicked">搜索</el-button>
      <!-- <slot name="action"></slot> -->
      <el-button v-if="isExpandBtn" class="expand" size="small" type="primary" text @click="expandClicked">
        {{ isExpand ? '展开' : '收起' }}
      </el-button>
    </div>
  </el-row>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/store/modules/app'
import 'element-plus/theme-chalk/display.css'
import { storeToRefs } from 'pinia'
import { throttle } from 'lodash-es'
import {handleTree} from "@/utils/tree";

defineOptions({ name: 'BasicFormSearch' })

const baseFormSearch = ref()
const appStore = useAppStore()
const { windowChangeCount } = storeToRefs(appStore)
const defaultSearchFormHeight = '46px'

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  data: {
    type: Object,
    required: true
  },
  permCode: {
    type: String,
    default: ''
  },
  classKey: {
    type: String,
    default: 'basic-form-search'
  }
})

const emit = defineEmits(['input', 'search', 'reset', 'update:data'])

let formData: any = ref(props.data)
let itemList: any = ref(props.config.itemList)
let isExpand = ref(false)
let isExpandBtn = ref(false)
const isFirstRender = ref(true)
const componentInfo: any = ref({
  type: '',
  info: {
    prop: '',
    onChange: Function
  }
})

// 设置默认值
const setDefaultValue = () => {
  formData.value = JSON.parse(JSON.stringify({ ...props.data }))
  emit('input', formData.value)
}

// const isColonFn = (item: { label: String; isColon: Boolean }) => {
//   return item.label && item.isColon ? '' : ':';
// };

const selectChangeByRoad = (val: any) => {
  if (!val) {
    delete formData.value.road
    emit('search', formData.value)
    return
  }
  const data = JSON.parse(JSON.stringify(formData.value))
  data.roadId = val.value
  delete data.road
  emit('search', data)
}

const searchClicked = throttle(() => {
  emit('search', formData.value)
}, 1000)

const resetClicked = throttle(() => {
  componentInfo.value.type = ''
  formData.value = {}
}, 1000)

const expandClicked = () => {
  const selectClass: any = document.querySelector(`.${props.classKey}`)
  if (isExpand.value) {
    // selectClass.style.setProperty('--max-height', `${selectClass.scrollHeight}px`)
    selectClass.style.maxHeight = `${selectClass.scrollHeight}px`
  } else {
    // selectClass.style.setProperty('--max-height', defaultSearchFormHeight)
    selectClass.style.maxHeight = defaultSearchFormHeight
  }
  isExpand.value = !isExpand.value
}

/** 清空表单 */
const isClearForm = ref(false)
const resetForm = () => {
  // TODO: 没用到表单，这边的方法作废
  // baseFormSearch.value.resetFields()
  isClearForm.value = true
  formData.value = {}
}

const handleFocus = (event: any, info: any) => {
  const className = event.currentTarget.id
  if (className === 'input') {
    componentInfo.value = { type: 'input', info }
  } else if (['select', 'datePicker'].includes(className)) {
    componentInfo.value = { type: 'select', info }
  }
  // else if (className === 'datePickerRange') {
  //   componentInfo.value = { type: 'range', info }
  // }
  else {
    componentInfo.value = { type: '', info }
  }
}

const init = () => {
  setDefaultValue()
  const selectClass: any = document.querySelector(`.${props.classKey}`)
  // selectClass.style.setProperty('--max-height', defaultSearchFormHeight)
  selectClass.style.maxHeight = defaultSearchFormHeight
  isExpandBtn.value = selectClass.scrollHeight > 55
  isExpand.value = isExpandBtn.value
  isFirstRender.value = true

  itemList.value?.forEach(async (info: any) => {
    if (info.api && (info.component === 'select' ||info.component === 'treeSelect')) {
      const res = await info.api()
      if (!info.fieldNames) {
        info.fieldNames = { label: 'label', value: 'value' }
      }
       const tempList = res.map((item: any) => {
        const newItem = {}
        for (let key in info.fieldNames) {
          newItem[key] = item[info.fieldNames[key]]
        }
        return newItem
      });
      if(info.component == 'select'){
        info.options = tempList
      }
      if(info.component == 'treeSelect'){
        info.options = handleTree(tempList);
      }
      console.log(info.options)
    }
  })
}

watch(windowChangeCount, () => {
  nextTick(() => {
    const selectClass: any = document.querySelector(`.${props.classKey}`)
    isExpandBtn.value = selectClass.scrollHeight > 55
    isExpand.value = isExpandBtn.value
  })
})

watch(
  () => props.config,
  (val) => {
    itemList.value = val.itemList
  },
  { deep: true }
)

watch(
  () => props.data,
  (val: any) => {
    formData.value = val
  },
  { deep: true }
)

watch(
  () => formData.value,
  (val: any) => {
    if (isFirstRender.value) {
      isFirstRender.value = false
      return
    }

    // ⚠️这边只是为了清空查询数据，但是不往外抛出搜索事件
    if (isClearForm.value) {
      isClearForm.value = false
      emit('update:data', val)
      return
    }

    // 日期范围组件单独判断
    // if (componentInfo.value.type === 'range' && val[componentInfo.value.info.prop]) {
    //   const temp = JSON.parse(JSON.stringify(val))
    //   if (!componentInfo.value.info.onChange) {
    //     const [start, end] = temp[componentInfo.value.info.prop]
    //     if (componentInfo.value.info.dateFormate === 'YYYY-MM-DD HH:mm:ss') {
    //       temp[componentInfo.value.info.prop] = [start, end.replace('00:00:00', '23:59:59')]
    //     }
    //     emit('update:data', temp)
    //     emit('search', temp)
    //   }
    //   return
    // }
    emit('update:data', val)
    if (!['input'].includes(componentInfo.value.type)) {
      emit('search', val)
    }
  },
  { deep: true }
)

onMounted(() => {
  init()
})

defineExpose({ resetForm })
</script>

<style lang="scss" scoped>
.basic-form-search {
  margin-left: 0 !important;
  width: 100%;

  overflow: hidden;
  transition: max-height 0.35s;
  min-width: var(--screen-min-width);
  //max-height: var(--max-height);
  text-align: left;

  .sub-item-btn {
    padding-top: 1px;
    margin-right: 0;
    padding-right: 0 !important;
    display: flex;
    justify-content: flex-end;
    .el-button {
      color: #1d2129;
      background-color: white;
      font-size: 13px;
      width: 50px !important;
      height: 30px !important;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .el-button--primary {
      color: white;
      background-color: var(--el-color-primary);
    }
    .is-text {
      color: var(--el-color-primary);
      background-color: transparent;
      padding: 0 4px !important;
      &:hover {
        background-color: transparent;
      }
    }
    .expand {
      padding: 5px 0;
      margin-left: 0;
    }
  }
}
</style>
