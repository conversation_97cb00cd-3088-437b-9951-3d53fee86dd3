<template>
  <div class="recycle-info-container">
    <div class="info-header">
      <h3 class="section-title">回收槽</h3>
    </div>

    <div class="recycle-content">
      <BasicFormSearch :config="searchConfig" v-model:data="searchForm" @search="loadRecycleList" />
      <BasicTable @register="register" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '@/components/BasicTable'
import BasicFormSearch from '@/components/BasicFormSearch/index.vue'
import { EmptyBottleRecordApi } from '@/api/system/smc/emptybottlerecord'

const props = defineProps({
  cabinetId: {
    type: String,
    required: true
  }
})

const searchForm = ref<{ [key: string]: any }>({})

// 搜索配置
const searchConfig = {
  itemList: [
    {
      component: 'input',
      label: '回收槽编号',
      prop: 'recycleSlotCode',
      placeholder: '请输入回收槽编号'
    },
    {
      component: 'select',
      label: '回收状态',
      prop: 'status',
      placeholder: '请选择回收状态',
      options: [
        { label: '空闲', value: 'idle' },
        { label: '使用中', value: 'in_use' },
        { label: '已满', value: 'full' },
        { label: '故障', value: 'fault' }
      ]
    }
  ]
}

// 表格配置
const tableColumns = [
  { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
  {
    label: '回收槽编号',
    prop: 'recycleSlotCode',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '回收槽位置',
    prop: 'position',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '容量',
    prop: 'capacity',
    tooltip: true,
    minWidth: 100
  },
  {
    label: '当前数量',
    prop: 'currentCount',
    tooltip: true,
    minWidth: 100
  },
  {
    label: '使用率',
    prop: 'usageRate',
    tooltip: true,
    minWidth: 100,
    filter: (row: any) => {
      if (row.capacity && row.currentCount) {
        const rate = ((row.currentCount / row.capacity) * 100).toFixed(1)
        return `${rate}%`
      }
      return '-'
    }
  },
  {
    label: '回收状态',
    prop: 'status',
    minWidth: 100,
    filter: (row: any) => {
      const statusMap = {
        'idle': '空闲',
        'in_use': '使用中',
        'full': '已满',
        'fault': '故障'
      }
      return statusMap[row.status] || row.status
    }
  },
  {
    label: '最后清空时间',
    prop: 'lastEmptyTime',
    dateFormate: 'YYYY-MM-DD HH:mm:ss',
    minWidth: 180
  },
  {
    label: '更新时间',
    prop: 'updateTime',
    dateFormate: 'YYYY-MM-DD HH:mm:ss',
    minWidth: 180
  }
]

const [register, { reload }] = useTable({
  api: EmptyBottleRecordApi.getEmptyBottleRecordPage,
  columns: tableColumns,
  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { 
      ...params, 
      ...searchForm.value,
      cabinetId: props.cabinetId 
    }
  }
})

const loadRecycleList = () => {
  reload()
}

onMounted(() => {
  loadRecycleList()
})
</script>

<style scoped lang="scss">
.recycle-info-container {
  .info-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .recycle-content {
    height: calc(100vh - 200px);
    overflow: auto;
  }
}
</style>
