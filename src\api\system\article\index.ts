import request from '@/config/axios'

// 新闻资讯 VO
export interface ArticleVO {
  id: number // 主键id
  title: string // 标题
  articleTypeId: number // 所属分类id
  videoId: string // 视频文件id
  mainImgIds: string // 主图文件ids
  description: string // 简介
  author: string // 作者
  originFrom: string // 来源
  publicDate: Date // 发布日期
  content: string // 详情内容
  attachmentIds: string // 附件ID
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  beHot: number // 是否热门 0=否,1=是
  sort: number // 排序
  remark: string // 备注
}

// 新闻资讯 API
export const ArticleApi = {
  // 查询新闻资讯分页
  getArticlePage: async (params: any) => {
    return await request.get({ url: `/system/article/page`, params })
  },

  // 查询新闻资讯详情
  getArticle: async (params: any) => {
    return await request.get({ url: `/system/article/get`, params })
  },

  // 新增新闻资讯
  createArticle: async (data: ArticleVO) => {
    return await request.post({ url: `/system/article/create`, data })
  },

  // 修改新闻资讯
  updateArticle: async (data: ArticleVO) => {
    return await request.put({ url: `/system/article/update`, data })
  },

  // 删除新闻资讯
  deleteArticle: async (id: number) => {
    return await request.delete({ url: `/system/article/delete?id=` + id })
  },

  // 导出新闻资讯 Excel
  exportArticle: async (params) => {
    return await request.download({ url: `/system/article/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/article/get-import-template` })
  },

  // 导入新闻资讯 Excel
  importArticle: async (formData) => {
    return await request.upload({ url: `/system/article/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/article/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },
}
