import { DICT_TYPE, getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '分类名',
      prop: 'name'
    },
    {
      component: 'input',
      label: '分类标志',
      prop: 'code'
    },
    {
      component: 'select',
      label: '分类状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '创建时间开始',
      endPlaceholder: '创建时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '分类编号', prop: 'id', minWidth: 60 },
    { label: '分类名', prop: 'name', minWidth: 180, tooltip: true },
    { label: '分类标志', prop: 'code', minWidth: 100, tooltip: true },
    { label: '分类描述', prop: 'description', minWidth: 140, tooltip: true },
    {
      label: '分类状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '分类排序', prop: 'sort', minWidth: 60 },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '分类名',
        prop: 'name',
        rules: [{ required: true, message: '分类名不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '分类标志',
        prop: 'code',
        rules: [{ required: true, message: '分类标志不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '分类状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '分类状态不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '分类排序',
        prop: 'sort',
        rules: [{ required: true, message: '分类排序不能为空', trigger: 'blur' }]
      }
    ]
  }
])
