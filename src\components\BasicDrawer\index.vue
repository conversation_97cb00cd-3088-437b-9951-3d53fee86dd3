<template>
  <div class="basic-form-drawer">
    <el-drawer
      v-model="innerVisible"
      append-to-body
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :close-on-click-modal="!showFooter"
      :size="width"
      :with-header="showHeader"
      class="basic-drawer"
      @close="closeHandle"
    >
      <template #header>
        <el-row justify="space-between" align="middle">
          <div class="header-title">{{ title }}</div>
          <slot name="header-action"></slot>
        </el-row>
      </template>
      <div class="drawer-content" :style="`text-align:${contentTextAlign}; height: ${contentHeight}`">
        <slot name="content"></slot>
      </div>
      <div v-if="showFooter" class="footer" :style="`justify-content:${btnsPosition}`">
        <slot name="action">
          <el-button @click="closeHandle">取消</el-button>
          <el-button type="primary" :loading="loading" @click="confirmHandle">确定</el-button>
          <slot name="btns"></slot>
        </slot>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup name="BasicFormDrawer">
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  show: propTypes.bool.def(false),
  loading: propTypes.bool.def(false),
  title: propTypes.string.def('标题'),
  width: propTypes.oneOfType([propTypes.string, propTypes.number]).def('30%'),
  btnPosition: propTypes.string.def('center'),
  showHeader: propTypes.bool.def(true),
  showFooter: propTypes.bool.def(true),
  contentTextAlign: propTypes.string.def('left')
})

const emit = defineEmits(['submit', 'update:show', 'close'])

const innerVisible = ref(false)

const btnsPosition = computed(() => {
  if (props.btnPosition === 'left') {
    return 'flex-start'
  } else if (props.btnPosition === 'center') {
    return 'center'
  } else {
    return 'flex-end'
  }
})

const contentHeight = computed(() => {
  return props.showFooter ? 'calc(100% - 50px)' : '100%'
})

watch(
  () => props.show,
  (val) => {
    if (!val) {
      emit('close')
    } else {
      top()
    }
    innerVisible.value = val
  },
  { immediate: true }
)

const confirmHandle = () => {
  emit('submit')
}
const closeHandle = () => {
  emit('update:show', false)
  emit('close')
}
function top() {
  nextTick(() => {
    const contentClass: any = document.querySelector('.drawer-content')
    if (contentClass) {
      contentClass.scrollTop = 0
    }
  })
}
</script>

<style lang="scss">
.basic-drawer .el-drawer__header {
  margin-bottom: 0;
  padding: 0 20px;
  height: 50px;
  border-bottom: 0.02em solid rgba(0, 0, 0, 0.06);
  .el-drawer__close-btn {
    padding-right: 2px;
    transition: all 0.2s;
    &:hover {
      transform: scale(1.4);
      color: var(--el-color-primary);
    }
  }
}
.basic-drawer .el-drawer__body {
  padding: 0;
}
</style>

<style lang="scss" scoped>
.basic-drawer {
  .header-title {
    text-align: left;
    color: #191919;
    font-size: 18px;
    font-weight: 500;
    margin-right: 20px;
  }
  .drawer-content {
    padding: 20px;
    height: calc(100% - 50px);
    overflow-x: hidden;
    overflow-y: auto;
  }
  .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    background-color: white;
    width: 100%;
    height: 50px;
    border-top: 0.02em solid rgba(0, 0, 0, 0.06);
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 20px;
    padding-right: 25px;
    .el-button:not(:first-child) {
      margin-left: 20px;
    }
    .el-button {
      color: #1d2129;
      background-color: white;
    }
    .el-button--primary {
      color: white;
      background-color: var(--el-color-primary);
    }
    .is-text {
      color: var(--el-color-primary);
      background-color: transparent;
      padding: 0 4px !important;
      &:hover {
        background-color: transparent;
      }
    }
  }
}
</style>
