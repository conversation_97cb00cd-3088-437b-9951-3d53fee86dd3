<template>
  <BasicPage :tabs="['部门管理']">
    <template #action>
      <el-button v-hasPermi="['system:dept:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register">
      <template #leader="{ row }">
        {{ userList.find((user) => user.id === row.leaderUserId)?.nickname }}
      </template>
    </BasicTable>
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()">
      <template #parentId>
        <el-tree-select
          v-model="formData.parentId"
          :data="deptTree"
          :props="defaultProps"
          check-strictly
          default-expand-all
          placeholder="请选择上级部门"
          value-key="deptId"
        />
      </template>
      <template #leaderUserId>
        <BasicSelect
          v-model="formData.leaderUserId"
          v-model:label="formData.leaderUser"
          :options="userList"
          :field-names="{ label: 'nickname', value: 'id' }"
          filterable
        />
      </template>
    </BasicFormDrawer>
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { defaultProps, handleTree } from '@/utils/tree'

defineOptions({ name: 'SystemDept' })

const message = useMessage()
const { t } = useI18n()

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const deptTree = ref() // 树形结构
const userList: any = ref([])
const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,

  buttons: [
    { label: '编辑', permCode: 'system:dept:update', callback: handleEdit },
    {
      label: '删除',
      permCode: 'system:dept:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}
const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: DeptApi.getDeptPage,
  columns: totalColumns,
  rowKey: 'id',
  pagination: false,
  beforeFetch: (params: any) => {
    return { pageNo: 1, pageSize: 100, ...params, ...searchForm.value }
  },
  afterFetch: (data: any) => {
    const list = handleTree(data)
    return list
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = async (id: number | null, type: string) => {
  userList.value = await UserApi.getSimpleUserList()
  await getTree()
  if (type == 'add') {
    formData.value.status = 0
  }
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: DeptApi.getDept,
    submitApi: id ? DeptApi.updateDept : DeptApi.createDept,
    afterFetch: (res: any) => {
      formData.value = res
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await DeptApi.deleteDept(id)
  message.success(t('common.delSuccess'))
  reload()
}
/** 获得部门树 */
const getTree = async () => {
  deptTree.value = []
  const data = await DeptApi.getSimpleDeptList()
  let dept: Tree = { id: 0, name: '顶级部门', children: [] }
  dept.children = handleTree(data)
  deptTree.value.push(dept)
}

/** 初始化 **/
onMounted(async () => {
  // 获取用户列表
  userList.value = await UserApi.getSimpleUserList()
})
</script>

<style></style>
