<template>
  <el-table ref="dragTable" :data="formData" :max-height="tableHeight" row-key="columnId" style="width: auto">
    <el-table-column
      :show-overflow-tooltip="true"
      label="字段列名"
      min-width="120"
      prop="columnName"
      fixed="left"
    />
    <el-table-column label="字段描述" min-width="120">
      <template #default="scope">
        <el-input v-model="scope.row.columnComment" />
      </template>
    </el-table-column>
    <el-table-column
      :show-overflow-tooltip="true"
      label="物理类型"
      min-width="100"
      prop="dataType"
    />
    <el-table-column label="Java类型" min-width="120">
      <template #default="scope">
        <el-select v-model="scope.row.javaType">
          <el-option label="Long" value="Long" />
          <el-option label="String" value="String" />
          <el-option label="Integer" value="Integer" />
          <el-option label="Double" value="Double" />
          <el-option label="BigDecimal" value="BigDecimal" />
          <el-option label="LocalDateTime" value="LocalDateTime" />
          <el-option label="Boolean" value="Boolean" />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="java属性" min-width="120">
      <template #default="scope">
        <el-input v-model="scope.row.javaField" />
      </template>
    </el-table-column>
    <el-table-column label="插入" min-width="40">
      <template #default="scope">
        <el-checkbox v-model="scope.row.createOperation" false-label="false" true-label="true" />
      </template>
    </el-table-column>
    <el-table-column label="编辑" min-width="40">
      <template #default="scope">
        <el-checkbox v-model="scope.row.updateOperation" false-label="false" true-label="true" />
      </template>
    </el-table-column>
    <el-table-column label="列表" min-width="40">
      <template #default="scope">
        <el-checkbox
          v-model="scope.row.listOperationResult"
          false-label="false"
          true-label="true"
        />
      </template>
    </el-table-column>
    <el-table-column label="查询" min-width="40">
      <template #default="scope">
        <el-checkbox v-model="scope.row.listOperation" false-label="false" true-label="true" />
      </template>
    </el-table-column>
    <el-table-column label="查询方式" min-width="100">
      <template #default="scope">
        <el-select v-model="scope.row.listOperationCondition">
          <el-option label="=" value="=" />
          <el-option label="!=" value="!=" />
          <el-option label=">" value=">" />
          <el-option label=">=" value=">=" />
          <el-option label="<" value="<>" />
          <el-option label="<=" value="<=" />
          <el-option label="LIKE" value="LIKE" />
          <el-option label="BETWEEN" value="BETWEEN" />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="允许空" min-width="80">
      <template #default="scope">
        <el-checkbox v-model="scope.row.nullable" false-label="false" true-label="true" />
      </template>
    </el-table-column>
    <el-table-column label="唯一性字段" min-width="80">
      <template #default="scope">
        <el-checkbox v-model="scope.row.beUnique" false-label="false" true-label="true" />
      </template>
    </el-table-column>
    <el-table-column label="显示类型" min-width="100">
      <template #default="scope">
        <el-select v-model="scope.row.htmlType">
          <el-option label="文本框" value="input" />
          <el-option label="数字" value="number" />
          <el-option label="文本域" value="textarea" />
          <el-option label="下拉框" value="select" />
          <el-option label="树形下拉框" value="treeSelect" />
          <el-option label="单选框" value="radio" />
          <el-option label="复选框" value="checkbox" />
          <el-option label="日期控件" value="datetime" />
          <el-option label="图片上传" value="imageUpload" />
          <el-option label="文件上传" value="fileUpload" />
          <el-option label="富文本控件" value="editor" />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="字典类型(和下拉选项关联表取其一，选择了字典会按字典生成代码)" min-width="120">
      <template #default="scope">
        <el-select v-model="scope.row.dictType" clearable filterable placeholder="请选择">
          <el-option
            v-for="dict in dictOptions"
            :key="dict.id"
            :label="dict.name"
            :value="dict.type"
          />
        </el-select>
      </template>
    </el-table-column>
    <el-table-column label="下拉选项关联表" min-width="150">
      <template #default="scope">
        <el-select v-model="scope.row.optionTableId" clearable filterable placeholder="请选择">
          <el-option
            v-for="(table0, index) in tables"
            :key="index"
            :label="table0.tableName + '：' + table0.tableComment"
            :value="table0.id"
          />
        </el-select>
      </template>
    </el-table-column>
    <!--    <el-table-column label="下拉选项表中label/value字段，select或treeSelect时必须" min-width="220">-->
    <!--      <template #default="scope">-->
    <!--        <el-select v-model="scope.row.optionLabelColumnId" clearable filterable placeholder="请选择label字段">-->
    <!--          <el-option-->
    <!--            v-for="dict in dictOptions"-->
    <!--            :key="dict.id"-->
    <!--            :label="dict.name"-->
    <!--            :value="dict.type"-->
    <!--          />-->
    <!--        </el-select>-->
    <!--&lt;!&ndash;      </template>&ndash;&gt;-->
    <!--&lt;!&ndash;    </el-table-column>&ndash;&gt;-->
    <!--&lt;!&ndash;    <el-table-column label="下拉选项表中value字段，select或treeSelect时必须" min-width="120">&ndash;&gt;-->
    <!--&lt;!&ndash;      <template #default="scope">&ndash;&gt;-->
    <!--        <br/>-->
    <!--        <el-select v-model="scope.row.optionValueColumnId" clearable filterable placeholder="请选择value字段">-->
    <!--          <el-option-->
    <!--            v-for="dict in dictOptions"-->
    <!--            :key="dict.id"-->
    <!--            :label="dict.name"-->
    <!--            :value="dict.type"-->
    <!--          />-->
    <!--        </el-select>-->
    <!--      </template>-->
    <!--    </el-table-column>-->
    <!--    <el-table-column label="下拉选项表中id字段,treeSelect时必须" min-width="220">-->
    <!--      <template #default="scope">-->
    <!--        <el-select v-model="scope.row.optionIdColumnId" clearable filterable placeholder="请选择id字段">-->
    <!--          <el-option-->
    <!--            v-for="dict in dictOptions"-->
    <!--            :key="dict.id"-->
    <!--            :label="dict.name"-->
    <!--            :value="dict.type"-->
    <!--          />-->
    <!--        </el-select>-->
    <!--&lt;!&ndash;      </template>&ndash;&gt;-->
    <!--&lt;!&ndash;    </el-table-column>&ndash;&gt;-->
    <!--&lt;!&ndash;    <el-table-column label="下拉选项表中parentId字段,treeSelect时必须" min-width="120">&ndash;&gt;-->
    <!--&lt;!&ndash;      <template #default="scope">&ndash;&gt;-->
    <!--        <el-select v-model="scope.row.optionParentColumnId" clearable filterable placeholder="请选择parentid字段">-->
    <!--          <el-option-->
    <!--            v-for="dict in dictOptions"-->
    <!--            :key="dict.id"-->
    <!--            :label="dict.name"-->
    <!--            :value="dict.type"-->
    <!--          />-->
    <!--        </el-select>-->
    <!--      </template>-->
    <!--    </el-table-column>-->
    <el-table-column label="下拉选项下拉选项是否可以多选" min-width="80">
      <template #default="scope">
        <el-checkbox v-model="scope.row.optionMultiple" false-label="false" true-label="true" />
      </template>
    </el-table-column>
    <el-table-column label="示例" min-width="100">
      <template #default="scope">
        <el-input v-model="scope.row.example" />
      </template>
    </el-table-column>
  </el-table>
</template>
<script lang="ts" setup>
  import { PropType } from 'vue'
  import * as CodegenApi from '@/api/infra/codegen'
  import * as DictDataApi from '@/api/system/dict/dict.type'

  defineOptions({ name: 'InfraCodegenColumInfoForm' })

  const props = defineProps({
    table: {
      type: Object as PropType<Nullable<CodegenApi.CodegenTableVO>>,
      default: () => null
    },
    columns: {
      type: Array as unknown as PropType<CodegenApi.CodegenColumnVO[]>,
      default: () => null
    }
  })

  const tables = ref([]) // 表定义列表
  const formData = ref<CodegenApi.CodegenColumnVO[]>([])
  const tableHeight = document.documentElement.scrollHeight - 350 + 'px'

  /** 查询字典下拉列表 */
  const dictOptions = ref<DictDataApi.DictTypeVO[]>()
  const getDictOptions = async () => {
    dictOptions.value = await DictDataApi.getSimpleDictTypeList({})
  }

  watch(
    () => props.columns,
    (columns) => {
      if (!columns) return
      formData.value = columns
    },
    {
      deep: true,
      immediate: true
    }
  )
  watch(
    () => props.table,
    async (table) => {
      if (!table) return
      // 加载表列表
      if (table.dataSourceConfigId >= 0) {
        tables.value = await CodegenApi.getCodegenTableList(table.dataSourceConfigId)
      }
    },
    {
      deep: true,
      immediate: true
    }
  )
  onMounted(async () => {
    await getDictOptions()
  })
</script>
