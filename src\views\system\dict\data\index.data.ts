import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      label: '字典名称',
      slot: 'name'
    },
    {
      component: 'input',
      label: '字典标签',
      prop: 'label'
    },
    {
      component: 'select',
      label: '状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    }
  ]
}

export const tableData: any = ref({
  columns: [
    // { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    { label: '字典编号', prop: 'id', minWidth: 60 },
    { label: '字典标签', prop: 'label', minWidth: 160, tooltip: true },
    { label: '字典键值', prop: 'value', minWidth: 180, tooltip: true },
    { label: '字典排序', prop: 'sort', minWidth: 80, tooltip: true },
    {
      label: '状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '颜色类型', prop: 'colorType', minWidth: 100, tooltip: true },
    { label: 'CSS Class', prop: 'cssClass', minWidth: 140, tooltip: true },
    { label: '备注', prop: 'remark', minWidth: 160, tooltip: true },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})
const colorTypeOptions = readonly([
  {
    value: 'default',
    label: '默认'
  },
  {
    value: 'primary',
    label: '主要'
  },
  {
    value: 'success',
    label: '成功'
  },
  {
    value: 'info',
    label: '信息'
  },
  {
    value: 'warning',
    label: '警告'
  },
  {
    value: 'danger',
    label: '危险'
  }
])
export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '字典类型',
        prop: 'dictType',
        readonly: true
      },
      {
        component: 'input',
        label: '数据标签',
        prop: 'label',
        rules: [{ required: true, message: '数据标签不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '数据键值',
        prop: 'value',
        rules: [{ required: true, message: '数据键值不能为空', trigger: 'blur' }]
      },
      {
        component: 'number',
        label: '显示排序',
        prop: 'sort',
        rules: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '颜色类型',
        prop: 'colorType',
        options: colorTypeOptions
      },
      {
        component: 'input',
        label: 'CSS Class',
        prop: 'cssClass'
      },
      {
        component: 'textarea',
        label: '备注',
        prop: 'remark'
      }
    ]
  }
])
