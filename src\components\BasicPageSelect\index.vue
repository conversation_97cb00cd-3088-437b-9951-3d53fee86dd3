<template>
  <el-select v-bind="$attrs" :loading="remoteLoading" remote-show-suffix :remote-method="remoteMethod">
    <template #loading>
      <div class="remote-loading">
        <div class="loader loader-loading"></div>
        <span>加载中...</span>
      </div>
    </template>
    <template #empty>
      <el-empty :image-size="60" :description="noDataText" />
    </template>
    <div
      v-infinite-scroll="loadMore"
      :infinite-scroll-delay="300"
      :infinite-scroll-immediate="true"
      :infinite-scroll-distance="20"
      class="infinite-list-wrapper"
    >
      <el-option
        v-for="item in list"
        :key="item.value"
        :label="item.label"
        :value="item"
        style="margin-top: 0; padding-top: 0"
      />
      <el-row v-if="loading" justify="center" align="middle" class="footer">
        <div class="loader loader-small"></div>
        <span>加载中...</span>
      </el-row>
    </div>
  </el-select>
  <!-- <el-select
    ref="pageSelectRef"
    v-bind="$attrs"
    :remote-method="remoteMethod"
    popper-class="page-select"
    @scroll="handleScroll"
  >
    <el-option
      v-for="item in list"
      :key="item.value"
      :label="item.label"
      :value="item"
      style="margin-top: 0; padding-top: 0"
    />
    <el-row v-if="loading" justify="center" align="middle" class="footer">
      <div class="loader"></div>
      <span>加载中...</span>
    </el-row>
  </el-select> -->
</template>

<script lang="ts" setup name="BasicPageSelect">
import { propTypes } from '@/utils/propTypes'
import { debounce } from 'lodash-es'

interface Item {
  value: number
  label: string
}

const props = defineProps({
  // 传入对应的列表加载api
  api: {
    type: Function,
    default: () => {}
  },
  params: propTypes.object.def({}),
  // 传入查询关键字
  searchKey: propTypes.string.def(''),
  fieldNames: propTypes.object.def({ label: 'label', value: 'value' }),
  noDataText: propTypes.string.def('暂无数据')
})

// const pageSelectRef = ref()
const loading = ref(true)
const remoteLoading = ref(false)
const list = ref<Item[]>([])
const queryFrom = ref({
  pageNo: 1,
  pageSize: 20,
  totlePage: 1
})

// 自定义远程搜索方法
const remoteMethod = async (query: string) => {
  remoteLoading.value = true
  list.value = []
  queryFrom.value.pageNo = 1
  queryFrom.value[props.searchKey] = query
  setTimeout(() => {
    getList()
  }, 10)
}

// 调用props.api
const getList = debounce(async () => {
  const tempParams = JSON.parse(JSON.stringify(queryFrom.value))
  delete tempParams.totlePage
  const params = {
    ...tempParams,
    ...props.params
  }
  const res = await props.api(params)
  const tempList = res.list.map((item: any) => {
    const newItem = {}
    for (let key in props.fieldNames) {
      newItem[key] = item[props.fieldNames[key]]
    }
    return newItem
  })
  list.value = [...list.value, ...tempList]

  queryFrom.value.totlePage = Math.ceil(res.total / queryFrom.value.pageSize)
  loading.value = list.value.length < res.total
  remoteLoading.value = false
}, 200)

// 无限滚动触底加载
const loadMore = () => {
  if (queryFrom.value.pageNo >= queryFrom.value.totlePage) return
  queryFrom.value.pageNo++
  getList()
}

// const handleScroll = async (event) => {
//   const { scrollTop, scrollHeight, clientHeight } = event.target
//   console.log(scrollTop + clientHeight, scrollHeight - 10)
//   if (scrollTop + clientHeight >= scrollHeight - 10) {
//     if (queryFrom.value.pageNo >= queryFrom.value.totlePage) return
//     queryFrom.value.pageNo++
//     getList()
//   }
// }

// const dropdownElement = ref()
// onMounted(async () => {
//   await nextTick()
//   dropdownElement.value = document.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
//   if (dropdownElement.value) {
//     dropdownElement.value.addEventListener('scroll', handleScroll)
//   }
// })

// onBeforeUnmount(() => {
//   debugger
//   if (dropdownElement.value) {
//     dropdownElement.value.removeEventListener('scroll', handleScroll)
//   }
// })

// 获取初始数据
// getList()
</script>

<style>
/* .page-select .el-select-dropdown__wrap {
  max-height: 200px;
  overflow: auto;
} */
</style>

<style lang="scss" scoped>
.infinite-list-wrapper {
  overflow: hidden;
}

.remote-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 180px;
  .loader-loading {
    width: 30px;
    height: 30px;
    margin-bottom: 15px;
  }
}

.footer {
  font-size: 12px;
  color: #999;
  padding: 8px 0;
  .loader-small {
    width: 10px;
    height: 10px;
    margin-right: 5px;
  }
}

.loader {
  border: 1.5px solid #999;
  border-left-color: transparent;
  border-radius: 50%;
  animation: spin89345 1s linear infinite;
}
@keyframes spin89345 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
