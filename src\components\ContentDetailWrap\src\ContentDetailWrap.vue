<template>
  <div ref="contentDetailWrap" class="content-detail-wrap">
    <el-row align="middle" class="header h-45px ml-10px">
      <ElButton text class="w-60px" @click="emit('back')">
        <Icon icon="ep:close" class="mr-4px" />{{ t('common.back') }}
      </ElButton>
      <el-divider direction="vertical" style="margin-right: 14px" />
      <div class="text-16px font-700">{{ title }}</div>
    </el-row>
    <div class="sub-content" :style="dynamicStyle">
      <slot></slot>
    </div>
    <el-row v-if="!hideButton" align="middle" class="footer">
      <el-button class="w-100px" type="primary" @click="emit('save')"> {{ saveText }} </el-button>
      <el-button class="w-100px">取 消</el-button>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

defineOptions({ name: 'ContentDetailWrap' })

const { t } = useI18n()
const emit = defineEmits(['back', 'save'])

const props = defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  hideButton: propTypes.bool.def(false),
  saveText: propTypes.string.def('保存')
})

const dynamicStyle = computed(() => {
  if (props.hideButton) {
    return { height: 'calc(100vh - var(--top-tool-height) - var(--tags-view-height) - var(--app-footer-height) - 28px - 45px)' }
  }
  return { height: 'calc(100vh - var(--top-tool-height) - var(--tags-view-height) - var(--app-footer-height) - 28px - 45px - 50px)' }
})
</script>

<style lang="scss" scoped>
$height: calc(100vh - 85px - 20px - 50px);

.content-detail-wrap {
  position: relative;
  min-height: $height;
  background-color: white;
  // border: 1px solid #e5e5e5;
  border-radius: 2px;
  overflow: hidden;
  .sub-content {
    padding: 0 14px 14px;
    overflow: auto;
  }
  .footer {
    z-index: 10;
    position: absolute;
    background-color: white;
    width: calc(100% - 24px);
    height: 50px;
    border-top: 1px solid #e5e5e5;
    margin: 0 12px;
    left: 0;
    bottom: 0;
  }
}
</style>
