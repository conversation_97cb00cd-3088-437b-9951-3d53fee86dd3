import request from '@/config/axios'

// 测试而已的类型 VO
export interface ForTestTypeVO {
  id: number // 主键id
  name: string // 名称
  parentId: number // 父类id
  iconUrl: string // 分类图标
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 测试而已的类型 API
export const ForTestTypeApi = {
  // 查询测试而已的类型分页
  getForTestTypePage: async (params: any) => {
    return await request.get({ url: `/system/for-test-type/page`, params })
  },
  // 查询测试而已的类型列表
  getForTestTypeList: async (params) => {
    return await request.get({ url: `/system/for-test-type/list`, params })
  },

  // 查询测试而已的类型详情
  getForTestType: async (params: any) => {
    return await request.get({ url: `/system/for-test-type/get`, params })
  },

  // 新增测试而已的类型
  createForTestType: async (data: ForTestTypeVO) => {
    return await request.post({ url: `/system/for-test-type/create`, data })
  },

  // 修改测试而已的类型
  updateForTestType: async (data: ForTestTypeVO) => {
    return await request.put({ url: `/system/for-test-type/update`, data })
  },

  // 删除测试而已的类型
  deleteForTestType: async (id: number) => {
    return await request.delete({ url: `/system/for-test-type/delete?id=` + id })
  },

  // 导出测试而已的类型 Excel
  exportForTestType: async (params) => {
    return await request.download({ url: `/system/for-test-type/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/for-test-type/get-import-template` })
  },

  // 导入测试而已的类型 Excel
  importForTestType: async (formData) => {
    return await request.upload({ url: `/system/for-test-type/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/for-test-type/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },
}
