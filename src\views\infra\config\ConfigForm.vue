<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="160px"
    >
      <el-form-item label="参数分类" prop="category">
        <el-input v-model="formData.category" placeholder="请输入参数分类" />
      </el-form-item>
      <el-form-item label="参数名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入参数名称" />
      </el-form-item>
      <el-form-item label="参数键名" prop="key">
        <el-input v-model="formData.key" placeholder="请输入参数键名(唯一)" />
      </el-form-item>
      <el-form-item label="键值类型" prop="configValueType">
        <el-select v-model="formData.configValueType" placeholder="请选择键值类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INFRA_CONFIG_VALUE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最大值" prop="maxLength">
        <el-input-number v-model="formData.maxLength" controls-position="right" :min="1" :max="999999999" :precision="0" style="width: 100%;" placeholder="请输入最大值(长度、图片张数)" />
      </el-form-item>
      <el-form-item label="最小值" prop="minLength">
        <el-input-number v-model="formData.minLength" controls-position="right" :min="1" :max="9999" :precision="0"  style="width: 100%;"  placeholder="请输入最小值（长度）" />
      </el-form-item>
      <el-form-item label="精度" prop="numPrecision">
        <el-input-number v-model="formData.numPrecision" controls-position="right"  :min="0" :max="10" :precision="0" style="width: 100%;"  placeholder="请输入精度" />
      </el-form-item>
      <el-form-item label="参数键值" prop="value">
        <UploadImg v-if="formData.configValueType == 'img'" v-model="formData.value" />
        <el-input-number v-else-if="formData.configValueType == 'number'" v-model="formData.value" controls-position="right" style="width: 100%;"
                         :min="formData.minLength" :max="formData.maxLength" :precision="formData.numPrecision" placeholder="请输入参数键值" />
        <el-input v-else-if="formData.configValueType == 'text'" v-model="formData.value"
                         :minlength="formData.minLength" :maxlength="formData.maxLength"
                         type="textarea"  :rows="3" :show-word-limit="true"  placeholder="请输入参数键值" />
        <Editor v-else-if="formData.configValueType == 'editor'" v-model="formData.value" ref="editor"  height="150px" />
        <el-input v-else  v-model="formData.value"
                  :minlength="formData.minLength" :maxlength="formData.maxLength" placeholder="请输入参数键值" />
      </el-form-item>
      <el-form-item label="是否可见" prop="visible">
        <el-radio-group v-model="formData.visible">
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value as string"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="系统内置" prop="type">
        <el-radio-group v-model="formData.type">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.INFRA_CONFIG_TYPE)"
            :key="dict.value as string"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入内容" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getBoolDictOptions,getStrDictOptions ,getIntDictOptions  } from '@/utils/dict'
import * as ConfigApi from '@/api/infra/config'

defineOptions({ name: 'InfraConfigForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  category: '',
  name: '',
  key: '',
  value: '',
  visible: true,
  type: 2,
  remark: '',
  configValueType: 'string',
  maxLength: 1,
  minLength: 1,
  numPrecision: 0,
  fileName: undefined,
})
const formRules = reactive({
  category: [{ required: true, message: '参数分类不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }],
  key: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }],
  value: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }],
  visible: [{ required: true, message: '是否可见不能为空', trigger: 'blur' }],
  type: [{ type: true, message: '是否系统内置不能为空', trigger: 'blur' }],
  configValueType: [{ required: true, message: '键值类型不能为空', trigger: 'blur' }],
  maxLength: [{ required: true, message: '最大值(长度、图片张数)不能为空', trigger: 'blur' }],
  minLength: [{ required: true, message: '最小值不能为空', trigger: 'blur' }],
  numPrecision: [{ required: true, message: '精度不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ConfigApi.getConfig({id:id})
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as ConfigApi.ConfigVO
    if (formType.value === 'create') {
      await ConfigApi.createConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await ConfigApi.updateConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    category: '',
    name: '',
    key: '',
    value: '',
    visible: true,
    type: 2,
    remark: '',
    configValueType: 'string',
    maxLength: 1,
    minLength: 1,
    numPrecision: 0,
    fileName: undefined,
  }
  formRef.value?.resetFields()
}
</script>
