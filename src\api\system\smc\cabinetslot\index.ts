import request from '@/config/axios'

// 药柜格口信息 VO
export interface CabinetSlotVO {
  id: number // 主键id
  cabinetId: number // 所属药柜ID
  drawerId: number // 所属抽屉ID
  slotName: string // 格口名称
  medicineId: number // 存放药品ID
  batchNumber: string // 单据药品批次号
  traceCode: string // 药品追溯码
  productionDate: Date // 生产日期
  shelfLife: number // 药品有效期(月)
  expiryDate: Date // 有效期截止日期
  currentQuantity: number // 当前数量
  slotVolume: number // 格口容积(立方米)
  safetyVolumeThreshold: number // 安全容积阈值(0-100)
  slotStatus: string // 格口状态:online/offline
  warningStatus: string // 格口告警状态
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 药柜格口信息 API
export const CabinetSlotApi = {
  // 查询药柜格口信息分页
  getCabinetSlotPage: async (params: any) => {
    return await request.get({ url: `/system/cabinet-slot/page`, params })
  },
  // 查询药柜格口信息列表
  getCabinetSlotList: async (params) => {
    return await request.get({ url: `/system/cabinet-slot/list`, params })
  },

  // 查询药柜格口信息详情
  getCabinetSlot: async (params: any) => {
    return await request.get({ url: `/system/cabinet-slot/get`, params })
  },

  // 新增药柜格口信息
  createCabinetSlot: async (data: CabinetSlotVO) => {
    return await request.post({ url: `/system/cabinet-slot/create`, data })
  },

  // 修改药柜格口信息
  updateCabinetSlot: async (data: CabinetSlotVO) => {
    return await request.put({ url: `/system/cabinet-slot/update`, data })
  },

  // 删除药柜格口信息
  deleteCabinetSlot: async (id: number) => {
    return await request.delete({ url: `/system/cabinet-slot/delete?id=` + id })
  },

  // 导出药柜格口信息 Excel
  exportCabinetSlot: async (params) => {
    return await request.download({ url: `/system/cabinet-slot/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/cabinet-slot/get-import-template` })
  },

  // 导入药柜格口信息 Excel
  importCabinetSlot: async (formData) => {
    return await request.upload({ url: `/system/cabinet-slot/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/cabinet-slot/import/downErrorFile`,
      data,
      method: 'POST'
    })
  }
}