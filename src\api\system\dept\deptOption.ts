import request from '@/config/axios'
// 代码生成时其他模块调用
// 部门 VO
export interface DeptVO {
  id: number // 部门id
  name: string // 部门名称
  parentId: number // 父部门id
  sort: number // 显示顺序
  leaderUserId: number // 负责人
  phone: string // 联系电话
  email: string // 邮箱
  status: number // 部门状态（0正常 1停用）
  deptCode: string // 部门编码(唯一)
  deptType: string // 部门属性 1:医院  20药库 30药房 40科室 100其他
}

// 部门 API
export const DeptApi = {
  // 查询部门分页
  getDeptPage: async (params: any) => {
    return await request.get({ url: `/system/dept/page`, params })
  },
  // 查询部门列表
  getDeptList: async (params) => {
    return await request.get({ url: `/system/dept/list`, params })
  },

  // 查询部门详情
  getDept: async (params: any) => {
    return await request.get({ url: `/system/dept/get`, params })
  },

}
