<template>
  <BasicPage :tabs="['药品出入柜操作记录管理']">
    <template #action>
      <BasicButtonImport perm-code="system:medicine-in-out-record:import" file-name="药品出入柜操作记录管理"
        :template-api="MedicineInOutRecordApi.importTemplate"
        :import-api="MedicineInOutRecordApi.importMedicineInOutRecord"
        :exportError-file-api="MedicineInOutRecordApi.exportErrorFile" @success="handlerImportSuccess" />
      <BasicButtonExport perm-code="system:medicine-in-out-record:export" file-name="药品出入柜操作记录管理"
        :params="{ ...searchForm }" :export-api="MedicineInOutRecordApi.exportMedicineInOutRecord" />
      <el-button v-hasPermi="['system:medicine-in-out-record:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch :config="formSearchConfig" v-model:data="searchForm" @search="() => reload()" />
    <BasicTable @register="register">
      <template #recognizedQuantity="{ row }">
        <span class="text-blue-600 cursor-pointer hover:underline" @click="handleViewRecognitionRecord(row)">
          {{ row.recognizedQuantity || '-' }}
        </span>
      </template>
      <template #videoUrl="{ row }">
        <el-button type="primary" size="small" @click="handleViewVideo(row.videoUrl)">
          查看视频
        </el-button>
      </template>
    </BasicTable>

    <!-- 抽屉表单 -->
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
    <!-- 弹窗表单 -->
    <!-- <BasicFormDialog ref="formDialogRef" @refresh="() => reload()" /> -->

    <!-- 识别记录弹窗 -->
    <el-dialog v-model="recognitionDialogVisible" :title="`${recognitionRecordData.medicineIdName || '药品'} 识别记录`"
      width="90%" :before-close="handleCloseRecognitionDialog">
      <div class="recognition-record-content">
        <!-- 使用与主列表相同的搜索组件 -->
        <BasicFormSearch :config="recognitionSearchConfig" v-model:data="recognitionSearchForm"
          @search="searchRecognitionData" />

        <BasicTable @register="recognitionTableRegister">
          <template #traceCode="{ row }">
            <span class="text-blue-600">{{ row.traceCode }}</span>
          </template>
        </BasicTable>


      </div>

      <template #footer>
        <el-button @click="recognitionDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig, recognitionSearchConfig, recognitionTableConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import BasicFormSearch from '@/components/BasicFormSearch/index.vue'
import { MedicineInOutRecordApi } from '@/api/system/smc/medicineinoutrecord'

const message = useMessage()

const formDialogRef = ref()
const formData = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const totalColumns = [...tableData.value.columns]

const [register, { reload }] = useTable({
  api: MedicineInOutRecordApi.getMedicineInOutRecordPage,
  columns: totalColumns,
  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (e) => {
    // 返回列表数据后，进行二次处理
    console.log(e)
  }
})

// 识别记录表格注册器
const [recognitionTableRegister, { reload: reloadRecognitionTable }] = useTable({
  api: MedicineInOutRecordApi.getRecognitionRecords,
  columns: recognitionTableConfig.columns,
  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return {
      ...params,
      ...recognitionSearchForm.value,
      recordId: recognitionRecordData.value.id
    }
  }
})

/** 导入成功 */
const handlerImportSuccess = () => {
  reload()
}
const openFormDialog = (id: number | null, type: string) => {
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: MedicineInOutRecordApi.getMedicineInOutRecord,
    submitApi: id ? MedicineInOutRecordApi.updateMedicineInOutRecord : MedicineInOutRecordApi.createMedicineInOutRecord
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

// 查看视频
function handleViewVideo(videoUrl: string) {
  if (videoUrl) {
    window.open(videoUrl, '_blank')
  } else {
    message.warning('视频链接为空')
  }
}

// 查看识别记录
const recognitionDialogVisible = ref(false)
const recognitionRecordData = ref<any>({})
const recognitionSearchForm = ref<{ [key: string]: any }>({})

function handleViewRecognitionRecord(row: any) {
  recognitionRecordData.value = row
  recognitionDialogVisible.value = true
  // 初始化搜索条件为空，让用户自己输入搜索条件
  recognitionSearchForm.value = {}
}

function handleCloseRecognitionDialog() {
  recognitionDialogVisible.value = false
  resetRecognitionSearch()
}

function searchRecognitionData() {
  // 触发表格重新加载
  reloadRecognitionTable()
}

function resetRecognitionSearch() {
  recognitionSearchForm.value = {}
}


</script>