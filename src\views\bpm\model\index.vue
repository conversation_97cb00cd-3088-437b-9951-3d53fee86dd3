<template>
  <BasicPage :tabs="['流程模型']">
    <template #action>
      <el-button v-hasPermi="['bpm:model:import']" @click="openImportForm">导入流程</el-button>
      <el-button v-hasPermi="['bpm:model:create']" @click="handleEdit">新建流程</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn">
      <template #category>
        <BasicSelect
          v-model="searchForm.category"
          :options="categoryList"
          placeholder="流程分类"
          filterable
          :field-names="{ label: 'name', value: 'code' }"
        />
      </template>
    </BasicFormSearch>
    <BasicTable @register="register">
      <template #name="{ row }">
        <el-button type="primary" link @click="handleBpmnDetail(row)">
          <span>{{ row.name }}</span>
        </el-button>
      </template>
      <template #icon="{ row }">
        <el-image :src="row.icon" class="w-32px h-32px" />
      </template>
      <template #formType="{ row }">
        <el-button v-if="row.formType === 10" type="primary" link @click="handleFormDetail(row)">
          <span>{{ row.formName }}</span>
        </el-button>
        <el-button v-else-if="row.formType === 20" type="primary" link @click="handleFormDetail(row)">
          <span>{{ row.formCustomCreatePath }}</span>
        </el-button>
        <label v-else>暂无表单</label>
      </template>
      <template #version="{ row }">
        <el-tag v-if="row.processDefinition">v{{ row.processDefinition.version }}</el-tag>
        <el-tag v-else type="warning">未部署</el-tag>
      </template>
      <template #suspensionState="{ row }">
        <el-switch
          v-if="row.processDefinition"
          v-model="row.processDefinition.suspensionState"
          :active-value="1"
          :inactive-value="2"
          @change="handleChangeState(row)"
        />
      </template>
    </BasicTable>

    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()">
      <template #category>
        <BasicSelect
          v-model="formData.category"
          :options="categoryList"
          placeholder="请选择流程分类"
          filterable
          :field-names="{ label: 'name', value: 'code' }"
        />
      </template>
      <template #icon>
        <UploadImg v-model="formData.icon" :limit="1" height="128px" width="128px" />
      </template>
      <template #formId>
        <BasicSelect
          v-model="formData.formId"
          :options="formList"
          placeholder="请选择流程表单"
          filterable
          :field-names="{ label: 'name', value: 'code' }"
        />
      </template>
    </BasicFormDrawer>
    <!-- 弹窗：流程模型图的预览 -->
    <Dialog title="流程图" v-model="bpmnDetailVisible" width="800">
      <MyProcessViewer key="designer" v-model="bpmnXML" :value="bpmnXML" v-bind="bpmnControlForm" :prefix="bpmnControlForm.prefix" />
    </Dialog>
    <!-- 表单弹窗：导入流程 -->
    <ModelImportForm ref="importFormRef" @success="reload" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, filterFormConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { CategoryApi } from '@/api/bpm/category'
import * as ModelApi from '@/api/bpm/model'
import * as FormApi from '@/api/bpm/form'
import { MyProcessViewer } from '@/components/bpmnProcessDesigner/package'
import { setConfAndFields2 } from '@/utils/formCreate'
import ModelImportForm from '@/views/bpm/model/ModelImportForm.vue'

defineOptions({ name: 'BpmModel' })

const message = useMessage()
const { t } = useI18n()
const { push } = useRouter() // 路由

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})
const categoryList = ref([]) // 流程分类列表
const formList = ref([]) // 流程表单的下拉框的数据

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 240,
  buttons: [
    { label: '编辑流程', permCode: 'bpm:model:update', callback: handleEdit },
    { label: '设计流程', permCode: 'bpm:model:update', callback: handleDesign },
    { label: '发布流程', permCode: 'bpm:model:deploy', callback: handleDeploy },
    { label: '流程定义', permCode: 'bpm:process-definition:query', callback: handleDefinitionList },
    {
      label: '删除',
      permCode: 'bpm:user-group:delete',
      color: 'red',
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: ModelApi.getModelPage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    return { ...params, ...searchForm.value }
  },
  afterFetch: (res) => {
    res.forEach((item) => {
      if (item.processDefinition) {
        item.deploymentTime = item.processDefinition.deploymentTime
      }
    })
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = async (id: number | null, type: string) => {
  if (type == 'add') {
    formData.value.status = 0
  } else {
    formList.value = await FormApi.getFormSimpleList()
  }
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: filterFormConfig(type),
    formData: formData.value,
    queryApi: ModelApi.getModel,
    submitApi: id ? ModelApi.updateModel : ModelApi.createModel,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    afterSumbit: async () => {
      if (type == 'create') {
        await ElMessageBox.alert(
          '<strong>新建模型成功！</strong>后续需要执行如下 3 个步骤：' +
            '<div>1. 点击【修改流程】按钮，配置流程的分类、表单信息</div>' +
            '<div>2. 点击【设计流程】按钮，绘制流程图</div>' +
            '<div>3. 点击【发布流程】按钮，完成流程的最终发布</div>' +
            '另外，每次流程修改后，都需要点击【发布流程】按钮，才能正式生效！！！',
          '重要提示',
          {
            dangerouslyUseHTMLString: true,
            type: 'success'
          }
        )
      }
    },
    change: (data) => {
      //流程表单
      if (data.formType == 10) {
        formDialogRef.value.setFormConfig(filterFormConfig(type, 'formId'))
      } else if (data.formType == 20) {
        formDialogRef.value.setFormConfig(filterFormConfig(type, 'formCustom'))
      }
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await ModelApi.deleteModel(id)
  message.success(t('common.delSuccess'))
  reload()
}

/** 添加/修改操作 */
const importFormRef = ref()
const openImportForm = () => {
  importFormRef.value.open()
}

/** 设计流程 */
function handleDesign(row) {
  push({
    name: 'BpmModelEditor',
    query: {
      modelId: row.id
    }
  })
}

/** 发布流程 */
async function handleDeploy(row) {
  try {
    // 删除的二次确认
    await message.confirm('是否部署该流程！！')
    // 发起部署
    await ModelApi.deployModel(row.id)
    message.success(t('部署成功'))
    // 刷新列表
    await reload()
  } catch {}
}

/** 跳转到指定 流程定义 列表 */
function handleDefinitionList(row) {
  push({
    name: 'BpmProcessDefinition',
    query: {
      key: row.key
    }
  })
}

/** 更新状态操作 */
const handleChangeState = async (row) => {
  const state = row.processDefinition.suspensionState
  try {
    // 修改状态的二次确认
    const id = row.id
    const statusState = state === 1 ? '激活' : '挂起'
    const content = '是否确认' + statusState + '流程名字为"' + row.name + '"的数据项?'
    await message.confirm(content)
    // 发起修改状态
    await ModelApi.updateModelState(id, state)
    // 刷新列表
    await reload()
  } catch {
    // 取消后，进行恢复按钮
    row.processDefinition.suspensionState = state === 1 ? 2 : 1
  }
}

/** 流程表单的详情按钮操作 */
const formDetailVisible = ref(false)
const formDetailPreview = ref({
  rule: [],
  option: {}
})
const handleFormDetail = async (row) => {
  if (row.formType == 10) {
    // 设置表单
    const data = await FormApi.getForm(row.formId)
    setConfAndFields2(formDetailPreview, data.conf, data.fields)
    // 弹窗打开
    formDetailVisible.value = true
  } else {
    await push({
      path: row.formCustomCreatePath
    })
  }
}

/** 流程图的详情按钮操作 */
const bpmnDetailVisible = ref(false)
const bpmnXML: any = ref(null)
const bpmnControlForm = ref({
  prefix: 'flowable'
})
const handleBpmnDetail = async (row) => {
  const data = await ModelApi.getModel({ id: row.id })
  bpmnXML.value = data.bpmnXml || ''
  bpmnDetailVisible.value = true
}

/** 初始化 **/
onMounted(async () => {
  // 加载用户列表
  categoryList.value = await CategoryApi.getCategorySimpleList()
})
</script>

<style></style>
