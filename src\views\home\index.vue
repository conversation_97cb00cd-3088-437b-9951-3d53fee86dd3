<template>
  <div class="workbench">
    <el-row class="mb-14px" style="min-width: 800px">
      <el-col>
        <div style="width: auto; text-align: center;margin: 100px;">
          <img key="1" alt="" class="w-350px" src="@/assets/svgs/login-box-bg.svg" />
          <div key="2" class="text-3xl">欢迎使用本系统</div>
        </div>
      </el-col>
    </el-row>

  </div>
</template>

<script lang="ts" setup>

</script>

<style lang="scss" scoped>
  :deep(.el-card__header) {
    padding: 0 15px 0 20px;
    height: 50px;
    line-height: 50px;
  }

  :deep(.custom-card) {
    padding: 10px 5px;
    height: 610px;
  }

  :deep(.custom-card2) {
    padding: 0 15px 15px;
  }

  :deep(.el-tabs--card .el-tabs__header) {
    border-bottom: none;
  }
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
    border: none;
    background: #f2f3f5;
    height: 40px;
    line-height: 40px;
    padding: 0 4px;
    display: flex;
    align-items: center;
  }
  :deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
    border: none;
    // margin-top: 4px;
  }

  :deep(.el-tabs__item.is-active) {
    background: #fff !important;
    height: 32px;
    // margin-top: 4px;
  }

  .project-info {
    padding-left: 10px;
    .code {
      color: #909399;
      font-size: 12px;
      font-weight: 400;
    }
  }
  :deep(.el-progress__text) {
    color: #303133;
  }
</style>
