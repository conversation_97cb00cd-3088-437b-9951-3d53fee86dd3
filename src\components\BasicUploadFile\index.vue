<template>
  <div>
    <div v-if="isView || detail">
      <div v-for="(item, index) in fileList" :key="index" :class="isView ? 'view-value' : 'detail-value'">
        <span v-if="!isImage(item)" class="cursor-pointer" @click="handlePreview(item)">{{ item.name }}</span>
        <div v-if="isImage(item)" style="padding-top: 10px">
          <BasicImageView :width="80" :height="80" :file-ids="item.id" />
        </div>
      </div>
      <div v-if="!fileList || fileList.length === 0" :class="isView ? 'view-value view-empty' : ''">--</div>
    </div>
    <el-upload ref="basicUploadRef" v-else v-model:file-list="fileList" :action="uploadUrl" :headers="requestHeaders"
      :auto-upload="isCopper ? false : true" :disabled="btnOkLoading" :limit="innerLimit"
      :multiple="isCopper ? false : innerMultiple" show-file-list class="basic-upload-file-uploader" name="file"
      :list-type="listType" :http-request="httpRequest" :before-upload="beforeUpload"
      :before-remove="handleBeforeRemove" :on-remove="handleRemove" :on-preview="handlePreview"
      :on-progress="handleProgress" :on-success="handleFileSuccess" :on-exceed="handleExceed" :on-error="handleError"
      :on-change="handleChange">
      <el-button>
        <Icon icon="ep:upload-filled" class="mr-2px" />
        上传文件
      </el-button>
      <template v-if="tip.length > 0" #tip>
        <div v-if="!allPermission">
          <span>
            大小不超过
            <b style="color: #f56c6c">{{ fileSize }}MB,</b>
          </span>
          <span>
            格式为
            <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
            的文件
          </span>
        </div>
        <span v-if="tip.length > 0" class="ml-10px text-12px text-gray-600">{{ tip }}</span>
      </template>
    </el-upload>

    <CopperModal ref="cropperModelRef" :srcValue="imageUrl" :fileName="imageName" @uploadSuccess="handleCopper" @close="copperDialogClose" />
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import { getAccessToken, getTenantId } from '@/utils/auth'
import { getFileInfoListByIds } from '@/api/infra/file'
import { throttle } from 'lodash-es'
import type { UploadFile, UploadFiles } from 'element-plus/es/components/upload/src/upload'
import { handleAuthorized } from '@/config/axios/service'
import * as imageConversion from 'image-conversion'
import CopperModal from './CopperModal.vue'
import { cloneDeep } from 'lodash-es'
import { updateFileInfo } from '@/api/infra/file'
// import { UploadRawFile, UploadRequestOptions } from 'element-plus/es/components/upload/src/upload'

import { useUploadNew } from '@/components/UploadFile/src/useUpload'

const { httpRequest } = useUploadNew()

const emit = defineEmits(['update:file-ids', 'update:file-list', 'success'])

const props = defineProps({
  fileIds: propTypes.string.def(''), // 文件ids
  fileType: propTypes.array.def(['doc', 'xls', 'ppt', 'txt', 'pdf']), // 文件类型, 例如['png', 'jpg', 'jpeg']
  listType: propTypes.string.def('text'), // 文件列表的类型, text / picture-card / picture
  fileSize: propTypes.number.def(5), // 大小限制(MB)
  fileWidth: propTypes.number.def(0), // 图片像素宽度
  limit: propTypes.number.def(0),
  multiple: propTypes.bool.def(true), // 是否多选
  allPermission: propTypes.bool.def(false), // 所有权限，不限制类型和大小
  tip: propTypes.string.def(''), // 提示信息
  isView: propTypes.bool.def(false), // 是否是查看状态
  detail: propTypes.bool.def(false), // 项目详情的回显状态
  noRepeat: propTypes.bool.def(false), // 获取多个文件信息，不过滤重复
  isCopper: { type: Boolean, default: false }, // 是否裁剪图片
  isCompress: propTypes.bool.def(false), // 是否压缩图片
})

const message = useMessage()

const basicUploadRef = ref()
const uploadUrl = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_API_URL}/infra/file/upload/info`
const btnOkLoading = ref(false)
const fileList: any = ref([])
// const uploadList: any = ref([])
const uploadNumber = ref(0)
const uploadFinish = ref(true)
// const removeTag = ref(false)
const cropperModelRef = ref()
const imageUrl: any = ref('')
const imageName = ref('')

const requestHeaders = computed(() => {
  return {
    Authorization: `Bearer ${getAccessToken()}`,
    'Tenant-Id': getTenantId()
  }
})

// 校验是否是图片格式
const isImage = (file: any) => {
  const extension = getFileExtension(file.url)
  return ['png', 'jpg', 'jpeg'].includes(extension)
}

// 从URL中提取文件扩展名
const getFileExtension = (url: string): string => {
  // 检查URL是否存在
  if (!url) {
    console.error('文件URL为空')
    return ''
  }

  // 尝试获取扩展名
  const parts = url.split('.')
  return parts.length > 1 ? parts.pop()!.toLowerCase() : ''
}

const innerLimit = computed(() => {
  if (props.allPermission) return 0
  return props.limit
})

const innerMultiple = computed(() => {
  if (props.allPermission) return true
  return props.multiple
})

const beforeUpload = async (file: any) => {
  if (!props.allPermission) {
    if (fileList.value.length >= props.limit) {
      message.error(`上传文件数量不能超过${props.limit}个!`)
      return false
    }
    let fileExtension = ''
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
    }
    if (props.fileType.length > 0) {
      const isImg = props.fileType.some((type: string) => {
        if (file.type.indexOf(type) > -1) return true
        return !!(fileExtension && fileExtension.indexOf(type) > -1)
      })
      if (!isImg) {
        message.error(`文件格式不正确, 请上传${props.fileType.join('/')}格式!`)
        return false
      }
    }
    const isLimit = file.size < props.fileSize * 1024 * 1024
    if (!isLimit) {
      if (props.isCompress) {
        return new Promise(async (resolve) => {
          let config = { size: props.fileSize * 700 }
          if (props.fileWidth > 0) {
            config = {
              width: props.fileWidth,
              size: props.fileSize * 700,
            }
          }
          const res = await imageConversion.compressAccurately(file, config);
          file = res;
          resolve(file);
        });
      } else {
        message.error(`上传文件大小不能超过${props.fileSize}MB!`)
        return false
      }
    }
  }
  uploadFinish.value = false
  uploadNumber.value++
  // handleUpload(file)
}

// 文件数超出提示
const handleExceed = () => {
  message.error(`上传文件数量不能超过${props.limit}个!`)
}

// 文件上传进度
const handleProgress = (event: any, uploadFile: any) => {
  console.log('上传进度', event.percent, uploadFile)
  if (event.percent >= 99) {
    event.percent = 99
    uploadFile.status = '"uploading"'
  }
}

const handlePreview = (file: any) => {
  window.open(file.url)
}

// 删除文件之前的校验
const handleBeforeRemove = (file: UploadFile, fileList: UploadFiles) => {
  const index = fileList.map((f: any) => f.name).indexOf(file.name)
  if (index > -1) {
    // removeTag.value = true
    fileList.splice(index, 1)
    const fileIds = fileList.map((f: any) => f.id)?.join(',') || ''
    // 双向绑定数据操作
    emit('update:file-ids', fileIds)
    return true
  }
  return false
}

// 删除上传文件
const handleRemove = () => {
  // 取消上传
  basicUploadRef.value.abort()
}

// 文件上传成功
const handleFileSuccess = (res: any) => {
  console.log('-----success---', res)
  const { code, data, msg } = res
  if (code === 401) {
    handleAuthorized()
    return
  }
  if (code !== 0) {
    message.error(msg)
    return
  }
  const index = fileList.value.findIndex((item: any) => item.name === data.name)
  fileList.value.splice(index, 1)

  fileList.value.push({
    id: data.id,
    name: data.name,
    url: data.url,
    size: data.size,
    status: 'success'
  })

  const fileIds = fileList.value.map((f: any) => f.id)?.join(',') || ''
  // 如果是多选，这边需要判断当前所选的id是否已经添加到列表中，如果列表中还存在空值，说明还未全部添加完成
  const existEmptyStr = fileIds.split(',').includes('')
  if (existEmptyStr) return
  emit('update:file-ids', fileIds)
  // emit('update:file-list', fileList.value)
  emit('success', fileList.value)
}

// 上传失败
const handleError = (error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  console.log('上传失败', error, uploadFile, uploadFiles)
}

// 文件状态改变
const handleChange = (file, fileList) => {
  if (file.status === 'ready' && props.isCopper) {
    const reader = new FileReader()
    reader.readAsDataURL(file.raw)
    reader.onload = function (e) {
      imageUrl.value = (e.target?.result as string) ?? ''
      imageName.value = file.name
      cropperModelRef.value.openModal()
    }
  }
}

// 图片裁剪完成
const handleCopper = async ({ source, data, filename }) => {
  let file = cloneDeep(data)
  const isLimit = data.size < props.fileSize * 1024 * 1024
  if (!isLimit) {
    if (props.isCompress) {
      let config = { size: props.fileSize * 700 }
      if (props.fileWidth > 0) {
        config = {
          width: props.fileWidth,
          size: props.fileSize * 700,
        }
      }
      const res = await imageConversion.compressAccurately(data, config);
      file = res;
      handleCopperUpload(source, file, filename)
    } else {
      message.error(`上传文件大小不能超过${props.fileSize}MB!`)
      return false
    }
  } else {
    handleCopperUpload(source, data, filename)
  }
}

// 图片裁剪上传
const handleCopperUpload = async (source: any, file: any, fileName: any) => {
  let filePath = new File([file], fileName);
  updateFileInfo({ file: filePath, name: fileName }).then((res: any) => {
    const index = fileList.value.findIndex((item: any) => item.name === fileName)
    fileList.value.splice(index, 1)
    fileList.value.push({
      id: res.data.id,
      name: fileName,
      url: res.data.url,
      size: res.data.size,
      status: 'success'
    })
    const fileIds = fileList.value.map((f: any) => f.id)?.join(',')
    emit('update:file-ids', fileIds)
    emit('success', fileList.value)
    cropperModelRef.value.closeModal()
  })
}

// 图片剪辑弹窗关闭
const copperDialogClose = (fileName:string) =>{
  const index = fileList.value.findIndex((item: any) => item.name === fileName)
  fileList.value.splice(index, 1)
}

const getFileInfoList = throttle(async (fileIds: any) => {
  const res = await getFileInfoListByIds({ fileIds, noRepeat: props.noRepeat })
  fileList.value = res.map((item: any) => {
    return {
      id: item.id,
      name: item.name,
      url: item.url,
      size: item.size,
      status: ''
    }
  })
  uploadFinish.value = true
}, 800)

watch(
  () => props.fileIds,
  async (ids: string) => {
    if (!ids || ids === '--') {
      fileList.value = []
      return
    }
    const fileIds = ids.toString().split(',').filter((id: string) => !['--', 'null'].includes(id)) || []
    if (fileIds.length === 0) {
      fileList.value = []
      return
    }
    getFileInfoList(fileIds)
  },
  { immediate: true }
)

defineExpose({ uploadFinish })
</script>

<style scoped>
.view-value {
  font-weight: 500;
  background-color: rgb(242, 243, 245);
  margin: 0 5px;
  padding: 0 8px;
  /* cursor: pointer; */
  color: var(--el-color-primary);
  transition: all 0.3s ease;

  &:not(:last-child) {
    margin-bottom: 4px;
  }

  &:hover {
    background-color: rgb(230, 231, 233);
  }
}

.detail-value {
  font-weight: 500;
  color: var(--el-color-primary);

  &:not(:last-child) {
    margin-bottom: 4px;
  }
}

.view-empty {
  color: #313131;
  cursor: default;

  &:hover {
    background-color: rgb(242, 243, 245);
  }
}
</style>
