<template>
  <div class="drawer-info-container">
    <div class="info-header">
      <h3 class="section-title">药柜抽屉</h3>
    </div>

    <div class="drawer-content">
      <BasicFormSearch :config="searchConfig" v-model:data="searchForm" @search="loadDrawerList" />
      <BasicTable @register="register" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { BasicTable, useTable } from '@/components/BasicTable'
import BasicFormSearch from '@/components/BasicFormSearch/index.vue'
import { CabinetDrawerApi } from '@/api/system/smc/cabinetdrawer'
import { DICT_TYPE, getDictLabel, getStrDictOptions } from '@/utils/dict'

const props = defineProps({
  cabinetId: {
    type: String,
    required: true
  }
})

const searchForm = ref<{ [key: string]: any }>({})

// 搜索配置
const searchConfig = {
  itemList: [
    {
      component: 'input',
      label: '抽屉编号',
      prop: 'drawerCode',
      placeholder: '请输入抽屉编号'
    },
    {
      component: 'input',
      label: '存放位置',
      prop: 'position',
      placeholder: '请输入存放位置'
    },
    {
      component: 'select',
      label: '抽屉类型',
      prop: 'drawerType',
      placeholder: '请选择抽屉类型',
      options: getStrDictOptions(DICT_TYPE.SMC_DRAWER_TYPE)
    },
    {
      component: 'select',
      label: '所属柜体',
      prop: 'cabinetBody',
      placeholder: '请选择所属柜体',
      options: getStrDictOptions(DICT_TYPE.SMC_CABINET_BODY)
    },
    {
      component: 'select',
      label: '抽屉用途',
      prop: 'drawerUsage',
      placeholder: '请选择抽屉用途',
      options: getStrDictOptions(DICT_TYPE.SMC_DRAWER_USAGE)
    },
    {
      component: 'select',
      label: '抽屉状态',
      prop: 'drawerStatus',
      placeholder: '请选择抽屉状态',
      options: getStrDictOptions(DICT_TYPE.SMC_ONLINE_STATUS)
    }
  ]
}

// 表格配置
const tableColumns = [
  { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
  {
    label: '抽屉编号',
    prop: 'drawerCode',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '抽屉名称',
    prop: 'drawerName',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '抽屉类型',
    prop: 'drawerType',
    tooltip: true,
    minWidth: 120,
    filter: (row: any) => {
      return getDictLabel(DICT_TYPE.SMC_DRAWER_TYPE, row.drawerType)
    }
  },
  {
    label: '抽屉状态',
    prop: 'drawerStatus',
    tooltip: true,
    minWidth: 120,
    filter: (row: any) => {
      return getDictLabel(DICT_TYPE.SMC_ONLINE_STATUS, row.drawerStatus)
    }
  },
  {
    label: '抽屉用途',
    prop: 'drawerUsage',
    tooltip: true,
    minWidth: 120,
    filter: (row: any) => {
      return getDictLabel(DICT_TYPE.SMC_DRAWER_USAGE, row.drawerUsage)
    }
  },
  {
    label: '抽屉位置',
    prop: 'position',
    tooltip: true,
    minWidth: 100
  },
  {
    label: '格口数量',
    prop: 'slotCount',
    tooltip: true,
    minWidth: 100
  },
  // {
  //   label: '已用格口',
  //   prop: 'usedSlotCount',
  //   tooltip: true,
  //   minWidth: 100
  // },
  {
    label: '所属柜体',
    prop: 'cabinetBody',
    tooltip: true,
    minWidth: 100,
    filter: (row: any) => {
      return getDictLabel(DICT_TYPE.SMC_CABINET_BODY, row.cabinetBody)
    }
  },
  {
    label: '格口容积(m³)',
    prop: 'slotVolume',
    tooltip: true,
    minWidth: 120
  },
  {
    label: '创建时间',
    prop: 'createTime',
    dateFormate: 'YYYY-MM-DD HH:mm:ss',
    minWidth: 180
  }
]

const [register, { reload }] = useTable({
  api: CabinetDrawerApi.getCabinetDrawerPage,
  columns: tableColumns,
  pagination: {
    pageSize: 10
  },
  beforeFetch: (params: any) => {
    return {
      ...params,
      ...searchForm.value,
      cabinetId: props.cabinetId
    }
  }
})

const loadDrawerList = () => {
  reload()
}

onMounted(() => {
  loadDrawerList()
})
</script>

<style scoped lang="scss">
.drawer-info-container {
  .info-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  .drawer-content {
    height: calc(100vh - 200px);
    overflow: auto;
  }
}
</style>
