import {
  DICT_TYPE,
  getIntDictOptions,
  getStrDictOptions,
  getDictOptions,
  getDictLabel,
  getBoolDictOptions
} from '@/utils/dict'
  import {DeptApi} from "@/api/system/dept/deptOption";

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '单据编号（唯一)',
      prop: 'documentNo',
      placeholder: '请输入单据编号（唯一)'
    },
    {
      component: 'select',
      label: '单据来源 his-his系统，self-本系统',
      prop: 'origin',
      placeholder: '请选择单据来源 his-his系统，self-本系统',
      params: {},
      filterable: true,
      options: getStrDictOptions(DICT_TYPE.SMC_ORIGIN)
    },
    {
      component: 'treeSelect',
      label: '入库部门ID',
      prop: 'intoDeptId',
      placeholder: '请选择入库部门ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: DeptApi.getDeptList,
    },
    {
      component: 'treeSelect',
      label: '出库部门ID',
      prop: 'outDeptId',
      placeholder: '请选择出库部门ID',
      params: {},
      filterable: true,
      fieldNames: { label: 'name', value: 'id', id: 'id', parentId: 'parentId' },
      api: DeptApi.getDeptList,
    },
    {
      component: 'select',
      label: '单据类型 10 药库入库单 20 药库销毁单 30 药库报损单 40药库出库单 50药房出库单 60药房退回单 70 科室退回单 200-分药单 -210医生取药单 220-医生退药单',
      prop: 'documentType',
      placeholder: '请选择单据类型 10 药库入库单 20 药库销毁单 30 药库报损单 40药库出库单 50药房出库单 60药房退回单 70 科室退回单 200-分药单 -210医生取药单 220-医生退药单',
      params: {},
      filterable: true,
      options: getStrDictOptions(DICT_TYPE.SMC_DOCUMENT_TYPE)
    },
    {
      component: 'datePickerRange',
      label: '同步时间',
      prop: 'syncTime',
      startPlaceholder: '同步时间开始日期',
      endPlaceholder: '同步时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      component: 'input',
      label: 'HIS同步的完整数据(JSON格式)',
      prop: 'hisSyncData',
      placeholder: '请输入HIS同步的完整数据(JSON格式)'
    },
    {
      component: 'input',
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）'
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      startPlaceholder: '创建时间开始日期',
      endPlaceholder: '创建时间结束日期',
      dateFormate: 'YYYY-MM-DD HH:mm:ss'
    },
  ]
}

export const tableData: any = ref({
  columns: [
    { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    {
      label: '主键id',
      prop: 'id',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单据编号（唯一)',
      prop: 'documentNo',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单据来源 his-his系统，self-本系统',
      prop: 'origin',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_ORIGIN, row.origin)
    },
    {
      label: '入库部门ID',
      prop: 'intoDeptIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '出库部门ID',
      prop: 'outDeptIdName',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '单据类型 10 药库入库单 20 药库销毁单 30 药库报损单 40药库出库单 50药房出库单 60药房退回单 70 科室退回单 200-分药单 -210医生取药单 220-医生退药单',
      prop: 'documentType',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.SMC_DOCUMENT_TYPE, row.documentType)
    },
    {
      label: '同步时间',
      prop: 'syncTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
    {
      label: 'HIS同步的完整数据(JSON格式)',
      prop: 'hisSyncData',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '是否已禁用 0=否（正常）,1=是（停用）',
      prop: 'status',
      minWidth: 100,
      filter: (row: any) => getDictLabel(DICT_TYPE.INFRA_BOOLEAN_STRING, row.status)
    },
    {
      label: '排序',
      prop: 'sort',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      tooltip: true,
      minWidth: 100
    },
    {
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      minWidth: 180
    },
  ]
})

export const formConfig = ref([
  {
    title: '单据',
    items: [
      {
        component: 'input',
        label: '单据编号（唯一)',
        prop: 'documentNo',
        placeholder: '请输入单据编号（唯一)',
        rules: [
              { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'select',
        label: '单据来源 his-his系统，self-本系统',
        prop: 'origin',
        placeholder: '请选择单据来源 his-his系统，self-本系统',
        params: {},
        filterable: true,
        options: getStrDictOptions(DICT_TYPE.SMC_ORIGIN),
      },
      {
        component: 'treeSelect',
        label: '入库部门ID',
        prop: 'intoDeptId',
        placeholder: '请选择入库部门ID',
        filterable: true,
        params: {},
        propName: 'intoDeptIdName',
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        api: DeptApi.getDeptList,
      },
      {
        component: 'treeSelect',
        label: '出库部门ID',
        prop: 'outDeptId',
        placeholder: '请选择出库部门ID',
        filterable: true,
        params: {},
        propName: 'outDeptIdName',
        fieldNames: { id:'id',parentId:'parentId', label: 'name', value: 'id' },
        api: DeptApi.getDeptList,
      },
      {
        component: 'select',
        label: '单据类型 10 药库入库单 20 药库销毁单 30 药库报损单 40药库出库单 50药房出库单 60药房退回单 70 科室退回单 200-分药单 -210医生取药单 220-医生退药单',
        prop: 'documentType',
        placeholder: '请选择单据类型 10 药库入库单 20 药库销毁单 30 药库报损单 40药库出库单 50药房出库单 60药房退回单 70 科室退回单 200-分药单 -210医生取药单 220-医生退药单',
        params: {},
        filterable: true,
        options: getStrDictOptions(DICT_TYPE.SMC_DOCUMENT_TYPE),
      },
      {
        component: 'datePicker',
        label: '同步时间',
        prop: 'syncTime',
        placeholder: '选择同步时间',
        dateFormate: 'YYYY-MM-DD HH:mm:ss',
        type: 'datetime',
      },
      {
        component: 'input',
        label: 'HIS同步的完整数据(JSON格式)',
        prop: 'hisSyncData',
        placeholder: '请输入HIS同步的完整数据(JSON格式)',
        rules: [
              { max: 65535, message: '长度不能超过65535个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'input',
        label: '是否已禁用 0=否（正常）,1=是（停用）',
        prop: 'status',
        placeholder: '请输入是否已禁用 0=否（正常）,1=是（停用）',
        rules: [
              { max: 3, message: '长度不能超过3个字符', trigger: 'blur' }
        ]
      },
      {
        component: 'number',
        label: '排序',
        prop: 'sort',
        max: 9999999999,
        placeholder: '请输入排序',
      },
      {
        component: 'input',
        label: '备注',
        prop: 'remark',
        placeholder: '请输入备注',
        rules: [
              { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
    ]
  }
])
