import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '用户名称',
      prop: 'username'
    },
    {
      component: 'input',
      label: '用户昵称',
      prop: 'nickname'
    },
    {
      component: 'input',
      label: '手机号码',
      prop: 'mobile'
    },
    {
      component: 'select',
      label: '状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '创建时间开始',
      endPlaceholder: '创建时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    { label: '用户编号', prop: 'id', minWidth: 80, tooltip: true },
    { label: '用户名称', prop: 'username', minWidth: 140, tooltip: true },
    { label: '工号', prop: 'workNo', minWidth: 140, tooltip: true },
    { label: '用户昵称', prop: 'nickname', minWidth: 140, tooltip: true },
    { label: '部门', prop: 'deptName', minWidth: 140, tooltip: true },
    { label: '手机号码', prop: 'mobile', minWidth: 140, tooltip: true },
    { label: '职位', prop: 'position', minWidth: 140, tooltip: true },
    {
      label: '状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '用户昵称',
        prop: 'nickname',
        rules: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }]
      },
      {
        label: '归属部门',
        slot: 'deptId',
        rules: [{ required: true, message: '归属部门不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '手机号码',
        prop: 'mobile',
        rules: [{ required: true, message: '手机号码不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '工号',
        prop: 'workNo',
        rules: [{ required: true, message: '工号不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '用户名称',
        prop: 'username',
        rules: [
            { required: true, message: '用户名称不能为空', trigger: 'blur' },
            {
              pattern: /^[a-zA-Z0-9]{3,30}$/,
              message: '用户账号由 数字、字母 组成, 长度3-30位',
              trigger: 'blur'
            }
          ]
      },
      {
        component: 'input',
        label: '用户密码',
        prop: 'password',
        type: 'password',
        rules: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          {
            pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?.&#^~])[A-Za-z\d@$!%*?.&#^~]{8,20}$/,
            message: '密码必须包含数字、字母和特殊字符(@$!%*?&#^~)，长度8-20位',
            trigger: 'blur'
          }
        ]
      },
      {
        label: '角色',
        prop: 'roleIds',
        slot: 'roleIds',
        rules: [{ required: true, message: '角色不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '职位',
        prop: 'position',
        // rules: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '邮箱',
        prop: 'email',
        // rules: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }]
      },
      // {
      //   label: '岗位',
      //   slot: 'postIds',
      //   rules: [{ required: true, message: '岗位不能为空', trigger: 'blur' }]
      // },
      {
        component: 'select',
        label: '用户性别',
        prop: 'sex',
        options: getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX),
        // rules: [{ required: true, message: '用户性别不能为空', trigger: 'blur' }]
      },
      {
        component: 'textarea',
        label: '备注',
        prop: 'remark'
      }
    ]
  }
])

export const filterFormConfig = (type: any) => {
  let config: any = []
  if (type == 'edit') {
    config = formConfig.value[0].items.filter((val: any) => !['username', 'password'].includes(val.prop))
  } else if (type == 'role') {
    config = formConfig.value[0].items.filter((val: any) => ['username', 'nickname'].includes(val.prop) || val.slot == 'roleIds')
  } else {
    config = formConfig.value[0].items
  }
  return [{ items: config }]
}
