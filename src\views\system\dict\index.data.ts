import { DICT_TYPE, getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
// import { amountRule } from '@/components/BasicFormSubmit/rules'

export const formSearchConfig = {
  itemList: [
    {
      component: 'input',
      label: '字典名称',
      prop: 'name'
    },
    {
      component: 'input',
      label: '字典类型',
      prop: 'type'
    },
    {
      component: 'select',
      label: '状态',
      prop: 'status',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    },
    {
      component: 'datePickerRange',
      label: '创建时间',
      prop: 'createTime',
      dateFormate: 'YYYY-MM-DD HH:mm:ss',
      startPlaceholder: '创建时间开始',
      endPlaceholder: '创建时间结束'
    }
  ]
}

export const tableData: any = ref({
  columns: [
    // { index: true, label: '序号', width: 60, filter: (index: number) => index + 1 },
    { label: '字典编号', prop: 'id', minWidth: 60 },
    { label: '字典名称', prop: 'name', minWidth: 160, tooltip: true },
    { label: '字典类型', prop: 'type', minWidth: 140, tooltip: true },
    {
      label: '状态',
      prop: 'status',
      minWidth: 120,
      dictType: DICT_TYPE.COMMON_STATUS
    },
    { label: '创建时间', minWidth: 180, prop: 'createTime' }
  ]
})

export const formConfig = ref([
  {
    // title: '管廊基础信息',
    items: [
      {
        component: 'input',
        label: '字典名称',
        prop: 'name',
        rules: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }]
      },
      {
        component: 'input',
        label: '字典类型',
        prop: 'type',
        readonly:true,
        rules: [{ required: true, message: '字典类型不能为空', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '是否内置',
        prop: 'beSystem',
        options: getIntDictOptions(DICT_TYPE.YES_OR_NO),
        rules: [{ required: true, message: '请选择是否内置', trigger: 'blur' }]
      },
      {
        component: 'select',
        label: '状态',
        prop: 'status',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS),
        rules: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      },
      {
        component: 'textarea',
        label: '备注',
        prop: 'remark'
      }
    ]
  }
])
