<template>
  <BasicPage :tabs="['字典管理']">
    <template #action>
      <BasicButtonExport
        perm-code="system:dict:export"
        file-name="字典管理"
        :params="{ ...searchForm }"
        :export-api="DictTypeApi.exportDictType"
      />
      <el-button v-if="userStore.getIsDeveloper" v-hasPermi="['system:dict:create']" @click="handleEdit">新增</el-button>
    </template>
    <BasicFormSearch perm-code="" :config="formSearchConfig" v-model:data="searchForm" @search="searchFn" />
    <BasicTable @register="register" />
    <BasicFormDrawer ref="formDialogRef" @refresh="() => reload()" />
  </BasicPage>
</template>

<script setup lang="ts">
import { formSearchConfig, tableData, formConfig } from './index.data'
import { BasicTable, useTable } from '@/components/BasicTable'
import { useUserStore } from '@/store/modules/user'
import * as DictType<PERSON>pi from '@/api/system/dict/dict.type'

defineOptions({ name: 'SystemDictType' })

const message = useMessage()
const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore() // 用户信息

const formDialogRef = ref()
const formData: any = ref({})
const searchForm = ref<{ [key: string]: any }>({})

const actionsColumn: any = {
  operation: true,
  label: '操作',
  width: 180,
  buttons: [
    {
      label: '编辑',
      hidden: () => {
        return userStore.getIsDeveloper
      },
      permCode: 'system:dict:update',
      callback: handleEdit
    },
    { label: '数据', permCode: '', callback: goPage },
    {
      label: '删除',
      permCode: 'system:dict:delete',
      color: 'red',
      hidden: () => {
        return userStore.getIsDeveloper
      },
      callback: handleDelete
    }
  ]
}

const totalColumns = [...tableData.value.columns, actionsColumn]

const [register, { reload }] = useTable({
  api: DictTypeApi.getDictTypePage,
  columns: totalColumns,
  pagination: { pageSize: 10 },
  beforeFetch: (params: any) => {
    if (!userStore.getIsDeveloper) {
      searchForm.value.beSystem = 0
    }
    if (searchForm.value.createTime) {
      searchForm.value.createTime[1] = searchForm.value.createTime[1].split(' ')[0] + ' 23:59:59'
    }
    return { ...params, ...searchForm.value }
  }
})

const searchFn = () => {
  reload()
}

const openFormDialog = (id: number | null, type: string) => {
  if (type == 'add') {
    formData.value.status = 0
    formData.value.beSystem = 0
    formConfig.value[0].items.forEach((val) => {
      if (val.prop == 'type') {
        val.readonly = false
      }
    })
  } else {
    formConfig.value[0].items.forEach((val) => {
      if (val.prop == 'type') {
        val.readonly = true
      }
    })
  }
  formDialogRef.value.init(id, {
    title: '',
    type,
    formConfigList: formConfig.value,
    formData: formData.value,
    queryApi: DictTypeApi.getDictType,
    submitApi: id ? DictTypeApi.updateDictType : DictTypeApi.createDictType,
    afterFetch: (res: any) => {
      formData.value = res
    },
    beforeSubmit: () => {
      // data.projectId = 32
    },
    onClose: () => {
      formData.value = {}
    }
  })
}

function handleEdit({ id }) {
  openFormDialog(id, id ? 'edit' : 'add')
}

function goPage(row) {
  router.push({ name: 'SystemDictData', params: { dictType: row.type, beSystem: row.beSystem } })
}

async function handleDelete({ id }) {
  await message.delConfirm()
  await DictTypeApi.deleteDictType(id)
  message.success(t('common.delSuccess'))
  reload()
}
</script>

<style></style>
