import request from '@/config/axios'

// app版本管理 VO
export interface AppVersionVO {
  id: number // 主键Id
  name: string // 版本名称
  content: string // 版本说明
  version: string // 版本号如 v1.0.0
  versionNum: number // 数字版本号如 1001
  url: string // 包地址
  appType: string // app应用类型 1安卓 2ios 3安卓pad 4 iosPad ...
  upgradeTime: Date // 升级更新时间
  remark: string // 备注
}

// app版本管理 API
export const AppVersionApi = {
  // 查询app版本管理分页
  getAppVersionPage: async (params: any) => {
    return await request.get({ url: `/system/app-version/page`, params })
  },

  // 查询app版本管理详情
  getAppVersion: async (params: any) => {
    return await request.get({ url: `/system/app-version/get`, params })
  },

  // 新增app版本管理
  createAppVersion: async (data: AppVersionVO) => {
    return await request.post({ url: `/system/app-version/create`, data })
  },

  // 修改app版本管理
  updateAppVersion: async (data: AppVersionVO) => {
    return await request.put({ url: `/system/app-version/update`, data })
  },

  // 删除app版本管理
  deleteAppVersion: async (id: number) => {
    return await request.delete({ url: `/system/app-version/delete?id=` + id })
  },

  // 导出app版本管理 Excel
  exportAppVersion: async (params) => {
    return await request.download({ url: `/system/app-version/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/app-version/get-import-template` })
  },

  // 导入app版本管理 Excel
  importAppVersion: async (formData) => {
    return await request.upload({ url: `/system/app-version/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/app-version/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },
}
