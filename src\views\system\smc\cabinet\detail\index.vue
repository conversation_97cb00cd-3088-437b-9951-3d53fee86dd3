<template>
  <div class="cabinet-detail-container">
    <!-- 顶部导航 -->
    <div class="detail-header">
      <el-button type="text" @click="goBack" class="back-btn">
        <Icon icon="ep:arrow-left" class="mr-1" />
        返回列表
      </el-button>
      <div class="header-info">
        <span class="cabinet-title">详情：药柜：{{ cabinetInfo.cabinetCode || '-' }}</span>
        <el-tag :type="cabinetInfo.onlineStatus === 'online' ? 'success' : 'danger'" class="ml-2">
          {{ cabinetInfo.onlineStatus === 'online' ? '在线' : '离线' }}
        </el-tag>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="detail-content">
      <!-- 左侧导航 -->
      <div class="sidebar">
        <div class="sidebar-menu">
          <div v-for="tab in tabs" :key="tab.key" :class="['menu-item', { active: activeTab === tab.key }]"
            @click="activeTab = tab.key">
            <Icon :icon="tab.icon" class="menu-icon" />
            <span class="menu-text">{{ tab.label }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 基本信息 -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <BasicInfo :cabinet-info="cabinetInfo" :loading="loading" @update-dept="handleUpdateDept" />
        </div>

        <!-- 药柜抽屉 -->
        <div v-if="activeTab === 'drawer'" class="tab-content">
          <DrawerInfo :cabinet-id="cabinetId" />
        </div>

        <!-- 一级药品 -->
        <div v-if="activeTab === 'medicine'" class="tab-content">
          <MedicineInfo :cabinet-id="cabinetId" />
        </div>

        <!-- 回收槽 -->
        <div v-if="activeTab === 'recycle'" class="tab-content">
          <RecycleInfo :cabinet-id="cabinetId" />
        </div>

        <!-- 其他部件 -->
        <div v-if="activeTab === 'components'" class="tab-content">
          <ComponentsInfo :cabinet-id="cabinetId" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CabinetApi } from '@/api/system/smc/cabinet'
import BasicInfo from './components/BasicInfo.vue'
import DrawerInfo from './components/DrawerInfo.vue'
import MedicineInfo from './components/MedicineInfo.vue'
import RecycleInfo from './components/RecycleInfo.vue'
import ComponentsInfo from './components/ComponentsInfo.vue'

const route = useRoute()
const router = useRouter()
const message = useMessage()

const cabinetId = computed(() => route.params.id as string)
const loading = ref(false)
const cabinetInfo = ref<any>({})
const activeTab = ref('basic')

// 页签配置
const tabs = [
  { key: 'basic', label: '基本信息', icon: 'ep:info-filled' },
  { key: 'drawer', label: '药柜抽屉', icon: 'ep:grid' },
  { key: 'medicine', label: '一级药品', icon: 'ep:medicine-box' },
  { key: 'recycle', label: '回收槽', icon: 'ep:delete' },
  { key: 'components', label: '其他部件', icon: 'ep:setting' }
]

// 获取药柜详情
const getCabinetDetail = async () => {
  if (!cabinetId.value) {
    message.error('药柜ID不能为空')
    return
  }

  try {
    loading.value = true

    // 调用 getCabinet API 接口，传入药柜ID
    const response = await CabinetApi.getCabinet({ id: cabinetId.value })

    console.log('API响应数据:', response)

    // 根据实际的API响应结构处理数据
    let cabinetData = null

    // 情况1: response直接是数据对象 (经过axios拦截器处理)
    if (response && typeof response === 'object') {
      // 如果response有data字段，说明是 {code: 200, data: {...}} 结构
      if (response.data !== undefined) {
        cabinetData = response.data
      }
      // 如果response没有data字段，但有其他业务字段，说明response本身就是数据
      else if (response.id || response.cabinetCode) {
        cabinetData = response
      }
    }

    if (cabinetData) {
      cabinetInfo.value = cabinetData
      console.log('获取药柜详情成功:', cabinetInfo.value)

      // 处理部门ID字符串转数组（用于表单编辑）
      if (cabinetInfo.value.deptIds && typeof cabinetInfo.value.deptIds === 'string') {
        cabinetInfo.value.deptIds = cabinetInfo.value.deptIds
          .split(',')
          .map((id: string) => parseInt(id.trim()))
          .filter((id: number) => !isNaN(id))
      }

      // 确保所有必要字段都有默认值
      cabinetInfo.value = {
        cabinetCode: cabinetInfo.value.cabinetCode || '',
        location: cabinetInfo.value.location || '',
        ip: cabinetInfo.value.ip || '',
        hardwareVersion: cabinetInfo.value.hardwareVersion || '',
        lastHeartbeat: cabinetInfo.value.lastHeartbeat || '',
        onlineStatus: cabinetInfo.value.onlineStatus || 'offline',
        deptIds: cabinetInfo.value.deptIds || [],
        deptIdsName: cabinetInfo.value.deptIdsName || '',
        createTime: cabinetInfo.value.createTime || '',
        ...cabinetInfo.value
      }
    } else {
      console.error('API响应数据格式异常:', response)
      message.error('获取药柜详情失败：数据格式错误')
    }
  } catch (error: any) {
    console.error('获取药柜详情失败:', error)
    const errorMessage = error?.response?.data?.msg || error?.message || '获取药柜详情失败'
    message.error(errorMessage)

    // 如果是404错误，跳转回列表页
    if (error?.response?.status === 404) {
      message.warning('药柜不存在，即将返回列表页')
      setTimeout(() => {
        goBack()
      }, 2000)
    }
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  try {

    // 直接使用浏览器历史记录返回
    router.go(-1)

  } catch (error) {
    console.error('返回失败:', error)
    // 备用方案：强制刷新页面，让浏览器回到上一页
    message.warning('返回失败，正在刷新页面')
    window.history.back()
  }
}

// 更新可使用部门
const handleUpdateDept = async (deptIds: number[]) => {
  try {
    const updateData = {
      ...cabinetInfo.value,
      deptIds: deptIds.join(',')
    }
    await CabinetApi.updateCabinet(updateData)
    message.success('更新成功')
    // 重新获取数据
    await getCabinetDetail()
  } catch (error) {
    console.error('更新失败:', error)
    message.error('更新失败')
  }
}

onMounted(() => {
  getCabinetDetail()
})
</script>

<style scoped lang="scss">
.cabinet-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.detail-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .back-btn {
    color: #1890ff;
    font-size: 14px;

    &:hover {
      color: #40a9ff;
    }
  }

  .header-info {
    display: flex;
    align-items: center;

    .cabinet-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }
}

.detail-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;

  .sidebar-menu {
    padding: 16px 0;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 12px 24px;
      cursor: pointer;
      transition: all 0.3s;
      border-right: 3px solid transparent;

      &:hover {
        background-color: #f0f0f0;
      }

      &.active {
        background-color: #e6f7ff;
        border-right-color: #1890ff;
        color: #1890ff;
      }

      .menu-icon {
        font-size: 16px;
        margin-right: 8px;
      }

      .menu-text {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.content-area {
  flex: 1;
  background: white;
  margin: 16px;
  border-radius: 6px;
  overflow: auto;

  .tab-content {
    padding: 24px;
    height: 100%;
  }
}
</style>
