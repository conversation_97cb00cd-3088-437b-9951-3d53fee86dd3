import request from '@/config/axios'

// 药品信息 VO
export interface MedicineVO {
  id: number // 主键id
  medicineName: string // 药品通用名称
  tradeName: string // 药品商品名称
  nationalDrugCode: string // 国家药品标识码（唯一）
  drugBaseCode: string // 药品本位码
  specification: string // 制剂规格
  dosageForm: string // 剂型
  unit: string // 单位
  boxVolume: number // 盒体体积(m³)
  boxWeight: number // 盒装重量(g)
  singleWeight: number // 单支重量(g)
  shelfLife: number // 药品有效期(月)
  packageSpecification: string // 包装规格
  packageConversionRatio: number // 包装转换比
  accessFrequency: string // 存取频率
  baseQuantity: number // 基数数量
  estimatedDays: number // 预计使用天数
  syncTime: Date // 同步时间
  hisSyncData: string // HIS同步的完整数据(JSON格式)
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  sort: number // 排序
  remark: string // 备注
}

// 药品信息 API
export const MedicineApi = {
  // 查询药品信息分页
  getMedicinePage: async (params: any) => {
    return await request.get({ url: `/system/medicine/page`, params })
  },
  // 查询药品信息列表
  getMedicineList: async (params) => {
    return await request.get({ url: `/system/medicine/list`, params })
  },

  // 查询药品信息详情
  getMedicine: async (params: any) => {
    return await request.get({ url: `/system/medicine/get`, params })
  },

  // 新增药品信息
  createMedicine: async (data: MedicineVO) => {
    return await request.post({ url: `/system/medicine/create`, data })
  },

  // 修改药品信息
  updateMedicine: async (data: MedicineVO) => {
    return await request.put({ url: `/system/medicine/update`, data })
  },

  // 删除药品信息
  deleteMedicine: async (id: number) => {
    return await request.delete({ url: `/system/medicine/delete?id=` + id })
  },

  // 导出药品信息 Excel
  exportMedicine: async (params) => {
    return await request.download({ url: `/system/medicine/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/medicine/get-import-template` })
  },

  // 导入药品信息 Excel
  importMedicine: async (formData) => {
    return await request.upload({ url: `/system/medicine/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/medicine/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },

  // 主动同步药品信息
  syncMedicine: async () => {
    return await request.post({ url: `/system/medicine/sync/his` })
  }
}