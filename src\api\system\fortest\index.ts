import request from '@/config/axios'

// 测试而已无用 VO
export interface ForTestVO {
  id: number // 主键id
  title: string // 标题
  articleTypeId: number // 所属分类id
  views: number // 浏览次数
  videoId: string // 视频文件id
  decialNum: number // 测试数字
  mainImgIds: string // 主图文件ids
  description: string // 简介
  author: string // 作者
  originFrom: string // 来源
  publicDate: Date // 发布日期
  content: string // 详情内容
  attachmentIds: string // 附件ID
  status: number // 是否已禁用 0=否（正常）,1=是（停用）
  beHot: number // 是否热门 0=否,1=是
  sort: number // 排序
  remark: string // 备注
}

// 测试而已无用 API
export const ForTestApi = {
  // 查询测试而已无用分页
  getForTestPage: async (params: any) => {
    return await request.get({ url: `/system/for-test/page`, params })
  },
  // 查询测试而已无用列表
  getForTestList: async (params) => {
    return await request.get({ url: `/system/for-test/list`, params })
  },

  // 查询测试而已无用详情
  getForTest: async (params: any) => {
    return await request.get({ url: `/system/for-test/get`, params })
  },

  // 新增测试而已无用
  createForTest: async (data: ForTestVO) => {
    return await request.post({ url: `/system/for-test/create`, data })
  },

  // 修改测试而已无用
  updateForTest: async (data: ForTestVO) => {
    return await request.put({ url: `/system/for-test/update`, data })
  },

  // 删除测试而已无用
  deleteForTest: async (id: number) => {
    return await request.delete({ url: `/system/for-test/delete?id=` + id })
  },

  // 导出测试而已无用 Excel
  exportForTest: async (params) => {
    return await request.download({ url: `/system/for-test/export-excel`, params })
  },

  // 下载模板
  importTemplate: async () => {
    return await request.download({ url: `/system/for-test/get-import-template` })
  },

  // 导入测试而已无用 Excel
  importForTest: async (formData) => {
    return await request.upload({ url: `/system/for-test/import`, data: formData })
  },

  // 导出错误信息文件
  exportErrorFile: async (data) => {
    return await request.download({
      url: `/system/for-test/import/downErrorFile`,
      data,
      method: 'POST'
    })
  },
}
